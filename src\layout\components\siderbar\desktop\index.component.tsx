import { SidebarItems } from "@/layout/hooks/sidebar/get-items.hook";
import { useSidebarExpansion } from "@/layout/hooks/sidebar/handle-expand-state.hook";
import { useItemsSidebar } from "@/layout/hooks/sidebar/items-sidebar.hook";
import { useLocation } from "@tanstack/react-router";
import { motion } from "framer-motion";
import { useAtomValue } from "jotai";
import React, { useEffect, useState } from "react";
import logoFadinha from "./image.png";

import { sidebarExpandAtom } from "@/layout/states/sidebar-expand.state";
import { SidebarToggleButton } from "./expand-toogle-button.component";
import { ItemMenu } from "./item-sidebar.component";

const getItems = new SidebarItems();

export const SidebarMain = () => {
	const { menuItems, itemActive, handleItemActive, configItems } = useItemsSidebar(getItems);
	const expandStatus = useAtomValue(sidebarExpandAtom);
	const { currentStateSidebar, toggleSidebar } = useSidebarExpansion();
	const [openItems, setOpenItems] = useState<Record<string, boolean>>({});
	const [activePath, setActivePath] = useState("");
	const [shouldAnimate, setShouldAnimate] = useState(false);
	const location = useLocation();

	useEffect(() => {
		setActivePath(location.pathname);
	}, [location.pathname]);

	useEffect(() => {
		const timer = setTimeout(() => {
			setShouldAnimate(true);
		}, 100);

		return () => clearTimeout(timer);
	}, []);

	const toggleItemOpen = (itemId: string) => {
		setOpenItems(prev => ({ ...prev, [itemId]: !prev[itemId] }));
	};

	const sidebarWidth = expandStatus === "expanded" ? 200 : 80;

	return (
		<motion.aside
			animate={{
				width: sidebarWidth,
				transition: shouldAnimate ? { type: "ease", stiffness: 300, damping: 15 } : { duration: 0 },
			}}
			className="h-screen p-2 sm:flex hidden"
			initial={{ width: shouldAnimate ? 80 : sidebarWidth }}
		>
			<div className="bg-white w-full rounded-[20px] shadow-md flex flex-col items-center h-full py-4 border border-blue-100 p-0">
				<div className="w-full flex justify-center items-center mb-6 relative">
					<svg
						width="56"
						height="43"
						viewBox="0 0 56 43"
						fill="none"
						xmlns="http://www.w3.org/2000/svg"
						xmlnsXlink="http://www.w3.org/1999/xlink"
					>
						<rect width="56" height="43" fill="url(#pattern0_95_2)" />
						<defs>
							<pattern id="pattern0_95_2" patternContentUnits="objectBoundingBox" width="1" height="1">
								<use xlinkHref="#image0_95_2" transform="matrix(0.00203137 0 0 0.0026455 -0.00174792 0)" />
							</pattern>
							<image id="image0_95_2" width="494" height="378" preserveAspectRatio="xMidYMid meet" href={logoFadinha} />
						</defs>
					</svg>
					{expandStatus === "expanded" && (
						<motion.span
							className="text-2xl text-mainColor font-semibold"
							initial={{ opacity: shouldAnimate ? 0 : 1 }}
							animate={{ opacity: 1 }}
							transition={shouldAnimate ? { duration: 0.2, delay: 0.1 } : { duration: 0 }}
						>
							LMP
						</motion.span>
					)}
				</div>
				<div className="flex flex-1 flex-col w-full items-center gap-3 px-2 flex-grow scrollbar-hide overflow-y-auto rounded-t-none">
					{menuItems.map(item => (
						<React.Fragment key={item.id}>
							<ItemMenu
								isActiveItem={item.id === itemActive.id}
								Icon={item.Icon}
								handleActiveItem={({ subItem }) => handleItemActive(item, subItem)}
								accessibleDescription={item.accessibleDescription}
								itemLabel={item.label}
								subItems={item.subItems}
								isOpen={!!openItems[item.id]}
								onToggle={() => toggleItemOpen(item.id)}
								activePath={activePath}
								setActivePath={setActivePath}
								shouldAnimate={shouldAnimate}
							/>
						</React.Fragment>
					))}
				</div>

				<div className="flex flex-col border-t-2 border-mainColor/10 items-center w-full px-2 gap-3 pt-3 mt-0 rounded-b-[20px] rounded-t-none flex-grow-0">
					<SidebarToggleButton currentStateSidebar={currentStateSidebar} toggleSidebar={toggleSidebar} />
					{configItems.map(item => (
						<React.Fragment key={item.id}>
							<ItemMenu
								isActiveItem={item.id === itemActive.id}
								Icon={item.Icon}
								handleActiveItem={() => handleItemActive(item)}
								accessibleDescription={item.accessibleDescription}
								itemLabel={item.label}
								subItems={item.subItems}
								isOpen={!!openItems[item.id]}
								onToggle={() => toggleItemOpen(item.id)}
								activePath={activePath}
								setActivePath={setActivePath}
								shouldAnimate={shouldAnimate}
							/>
						</React.Fragment>
					))}
				</div>
			</div>
		</motion.aside>
	);
};
