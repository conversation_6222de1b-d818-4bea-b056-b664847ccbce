import { CardLMPContainer } from "@/shared/components/custom/card";
import { FileText, Download, Eye } from "lucide-react";
import { motion } from "framer-motion";
import { IDashboardReport } from "../../types/dashboard.types";
import { useResponsiveCharts } from "../../hooks/use-responsive-charts";

import React from "react";
import { ReportModal } from "./report-modal";

interface IReportsSectionProps {
	reports: IDashboardReport[];
	activeReport: number | null;
	onOpenReport: (reportId: number) => void;
	onCloseReport: () => void;
	onGeneratePDF: (reportId: number) => void;
}

const ReportCard = React.memo(
	({
		report,
		onOpenReport,
		onGeneratePDF,
	}: {
		report: IDashboardReport;
		onOpenReport: (id: number) => void;
		onGeneratePDF: (id: number) => void;
	}) => {
		const { isMobile } = useResponsiveCharts();

		return (
			<motion.div
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				className={`
					relative flex items-center justify-between
					${isMobile ? "p-3" : "p-4"}
					bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group
					${isMobile ? "flex-col gap-3" : "flex-row"}
				`}
			>
				<div className={`flex items-center gap-3 flex-1 ${isMobile ? "w-full" : ""}`}>
					<div
						className={`
						${isMobile ? "p-1.5" : "p-2"}
						bg-white rounded-lg shadow-sm group-hover:shadow transition-shadow
					`}
					>
						<FileText size={isMobile ? 16 : 18} className="text-gray-600" />
					</div>
					<div className="flex-1 min-w-0">
						<h4
							className={`
							font-medium text-gray-900 truncate
							${isMobile ? "text-sm" : "text-base"}
						`}
						>
							{report.nome}
						</h4>
						<p
							className={`
							text-gray-500 truncate
							${isMobile ? "text-xs" : "text-sm"}
						`}
						>
							{report.descricao}
						</p>
					</div>
				</div>

				{/* Desktop Actions */}
				{!isMobile && (
					<div className="flex items-center gap-2 ml-4">
						<button
							onClick={() => onOpenReport(report.id)}
							className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
							title="Visualizar relatório"
						>
							<Eye size={16} />
						</button>
						<button
							onClick={() => onGeneratePDF(report.id)}
							className="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
							title="Baixar PDF"
						>
							<Download size={16} />
						</button>
					</div>
				)}

				{/* Mobile Actions */}
				{isMobile && (
					<div className="flex items-center gap-2 w-full">
						<button
							onClick={() => onOpenReport(report.id)}
							className="flex-1 flex items-center justify-center gap-2 p-2.5 text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors text-sm font-medium"
						>
							<Eye size={14} />
							Visualizar
						</button>
						<button
							onClick={() => onGeneratePDF(report.id)}
							className="flex-1 flex items-center justify-center gap-2 p-2.5 text-green-600 bg-green-50 hover:bg-green-100 rounded-lg transition-colors text-sm font-medium"
						>
							<Download size={14} />
							Baixar PDF
						</button>
					</div>
				)}
			</motion.div>
		);
	}
);

ReportCard.displayName = "ReportCard";

export const ReportsSection = React.memo(({ reports, activeReport, onOpenReport, onCloseReport, onGeneratePDF }: IReportsSectionProps) => {
	const { isMobile } = useResponsiveCharts();

	return (
		<>
			<CardLMPContainer
				icon={<FileText size={isMobile ? 20 : 22} className="text-gray-500" />}
				title="Relatórios"
				description={isMobile ? "Relatórios do seu negócio" : "Acesse e gere relatórios detalhados do seu negócio"}
				actions={
					!isMobile && (
						<button className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 transition-colors">
							<FileText size={16} />
							Todos os relatórios
						</button>
					)
				}
			>
				<div className={`space-y-${isMobile ? "2" : "3"}`}>
					{reports.map((report, index) => (
						<motion.div
							key={report.id}
							initial={{ opacity: 0, x: -20 }}
							animate={{ opacity: 1, x: 0 }}
							transition={{
								duration: isMobile ? 0.2 : 0.3,
								delay: isMobile ? index * 0.05 : index * 0.1,
							}}
						>
							<ReportCard report={report} onOpenReport={onOpenReport} onGeneratePDF={onGeneratePDF} />
						</motion.div>
					))}
				</div>

				{/* Mobile "Ver todos" button */}
				{isMobile && (
					<div className="mt-4 pt-4 border-t border-gray-200">
						<button className="w-full flex items-center justify-center gap-2 text-sm text-blue-600 hover:text-blue-700 transition-colors py-3 bg-blue-50 hover:bg-blue-100 rounded-lg font-medium">
							<FileText size={16} />
							Ver todos os relatórios
						</button>
					</div>
				)}
			</CardLMPContainer>

			{activeReport && <ReportModal reportId={activeReport} reports={reports} onClose={onCloseReport} onGeneratePDF={onGeneratePDF} />}
		</>
	);
});

ReportsSection.displayName = "ReportsSection";
