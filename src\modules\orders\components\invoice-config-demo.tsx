import { Button } from "@/shared/components/ui/button";
import { useState } from "react";
import { InvoiceConfigModal, InvoiceConfig } from "./invoice-config-modal";

export const InvoiceConfigDemo = () => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [isLoading, setIsLoading] = useState(false);

	const handleOpenModal = () => {
		setIsModalOpen(true);
	};

	const handleCloseModal = () => {
		setIsModalOpen(false);
	};

	const handleConfirm = async (config: InvoiceConfig) => {
		setIsLoading(true);
		
		// Simular envio para API
		console.log("Configuração da nota fiscal:", config);
		
		// Simular delay da API
		await new Promise(resolve => setTimeout(resolve, 2000));
		
		setIsLoading(false);
		setIsModalOpen(false);
		
		alert("Pedido enviado para faturamento com sucesso!");
	};

	return (
		<div className="p-6">
			<h2 className="text-xl font-semibold mb-4">Demo - Modal de Configuração da Nota Fiscal</h2>
			<p className="text-gray-600 mb-6">
				Clique no botão abaixo para abrir o modal de configuração da nota fiscal.
			</p>
			
			<Button onClick={handleOpenModal} className="bg-blue-600 hover:bg-blue-700">
				Enviar para Faturamento
			</Button>

			<InvoiceConfigModal
				isOpen={isModalOpen}
				onClose={handleCloseModal}
				onConfirm={handleConfirm}
				isLoading={isLoading}
			/>
		</div>
	);
};
