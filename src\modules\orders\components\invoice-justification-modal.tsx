import { useState } from "react";
import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { Button } from "@/shared/components/ui/button";
import { AlertCircle } from "lucide-react";
import { motion } from "framer-motion";

interface InvoiceJustificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (justification: string) => void;
  loading?: boolean;
}

export function InvoiceJustificationModal({ isOpen, onClose, onSubmit, loading }: InvoiceJustificationModalProps) {
  const [justification, setJustification] = useState("");
  const [touched, setTouched] = useState(false);
  const isValid = justification.trim().length > 0;

  const handleConfirm = () => {
    setTouched(true);
    if (isValid) {
      onSubmit(justification.trim());
      setJustification("");
      setTouched(false);
    }
  };

  const handleClose = () => {
    setJustification("");
    setTouched(false);
    onClose();
  };

  return (
    <OverlayContainer isVisible={isOpen} onClose={handleClose}>
      <div className="flex flex-col items-center justify-center min-h-screen">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.25, ease: "easeOut" }}
          className="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-xl border border-gray-100 relative animate-fade-in"
        >
          <div className="flex flex-col items-center mb-6">
            <div className="bg-red-100 p-3 rounded-full mb-2 animate-pulse">
              <AlertCircle size={36} className="text-red-500" />
            </div>
            <h2 className="text-2xl font-bold text-gray-800 mb-1 text-center">Justificativa para Invalidação</h2>
            <p className="text-gray-500 text-center text-base">Explique o motivo da invalidação da nota fiscal. Essa ação é irreversível.</p>
          </div>
          <textarea
            className="w-full border-2 border-gray-200 focus:ring-2 focus:ring-red-200 rounded-lg p-3 min-h-[100px] text-base transition-all duration-200 outline-none resize-none shadow-sm"
            placeholder="Descreva o motivo da invalidação da nota fiscal..."
            value={justification}
            onChange={e => setJustification(e.target.value)}
            onBlur={() => setTouched(true)}
            disabled={loading}
            maxLength={500}
          />
          <div className="flex justify-between items-center mt-2">
            <span className={`text-xs ${justification.length > 450 ? 'text-red-400' : 'text-gray-400'}`}>{justification.length}/500</span>
            {touched && !isValid && (
              <span className="text-red-500 text-xs ml-2">A justificativa é obrigatória.</span>
            )}
          </div>
          <div className="flex justify-end gap-3 mt-8">
            <Button onClick={handleClose} disabled={loading} variant="secondary" className="px-6 py-2 rounded-lg">Cancelar</Button>
            <Button
              onClick={handleConfirm}
              disabled={!isValid || loading}
              className="px-6 py-2 rounded-lg bg-red-500 hover:bg-red-600 text-white font-semibold shadow-md transition-all duration-200"
            >
              {loading ? (
                <span className="flex items-center gap-2"><span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>Enviando...</span>
              ) : (
                "Confirmar"
              )}
            </Button>
          </div>
        </motion.div>
      </div>
    </OverlayContainer>
  );
}
