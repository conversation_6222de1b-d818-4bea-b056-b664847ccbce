import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { createStockMovementRequest } from "../../api/requests/create-movement";
import { ICreateStockMovementDto } from "../../dtos/create-movement.dto";

export const useCreateStockMovement = () => {
	const queryClient = useQueryClient();

	const mutation = useMutation<ApiResponse<IGlobalMessageReturn>, Error, ICreateStockMovementDto>({
		mutationKey: ["create-stock-movement"],
		mutationFn: async (data: ICreateStockMovementDto) => {
			toast.loading("Criando movimento de estoque...");
			const response = await createStockMovementRequest(data);

			if (!response.success) {
				throw new Error(response.data.message);
			}

			return response;
		},
		onSuccess: ({ data }) => {
			toast.dismiss();
			toast.success(data.message);
			queryClient.invalidateQueries({
				queryKey: ["stock-movements"],
				exact: false,
			});
			queryClient.invalidateQueries({
				queryKey: ["stock-find-all"],
				exact: false,
			});
		},
		onError: error => {
			toast.dismiss();
			toast.error(error.message || "Erro ao criar movimento de estoque");
		},
	});

	const createMovement = (data: ICreateStockMovementDto) => {
		mutation.mutate(data);
	};

	return {
		createMovement,
		mutateAsync: mutation.mutateAsync,
		isPending: mutation.isPending,
	};
};
