import { useCallback, useMemo, useRef } from "react";
import { FieldErrors, FieldValues } from "react-hook-form";

interface IUseScrollToErrorHook<T extends FieldValues> {
	errors: FieldErrors<T>;
	arrayFieldName: keyof T;
}

interface ErrorSummary {
	totalErrors: number;
	itemsWithErrors: number;
	errorsByItem: Array<{
		index: number;
		errorCount: number;
		errorFields: string[];
	}>;
	firstErrorIndex: number;
}

export function useScrollToError<T extends FieldValues>({ errors, arrayFieldName }: IUseScrollToErrorHook<T>) {
	const itemRefs = useRef<(HTMLElement | null)[]>([]);

	const errorSummary = useMemo((): ErrorSummary => {
		const arrayErrors = errors[arrayFieldName] as FieldErrors[] | undefined;

		if (!arrayErrors) {
			return {
				totalErrors: 0,
				itemsWithErrors: 0,
				errorsByItem: [],
				firstErrorIndex: -1,
			};
		}

		const errorsByItem: ErrorSummary["errorsByItem"] = [];
		let totalErrors = 0;
		let firstErrorIndex = -1;

		for (let i = 0; i < arrayErrors.length; i++) {
			const itemErrors = arrayErrors[i];
			if (itemErrors) {
				if (firstErrorIndex === -1) {
					firstErrorIndex = i;
				}

				const errorFields = getErrorFields(itemErrors);
				const errorCount = errorFields.length;

				errorsByItem.push({
					index: i,
					errorCount,
					errorFields,
				});

				totalErrors += errorCount;
			}
		}

		return {
			totalErrors,
			itemsWithErrors: errorsByItem.length,
			errorsByItem,
			firstErrorIndex,
		};
	}, [errors, arrayFieldName]);

	const findFirstErrorIndex = useCallback((): number => {
		return errorSummary.firstErrorIndex;
	}, [errorSummary.firstErrorIndex]);

	const scrollToFirstError = useCallback(() => {
		const firstErrorIndex = findFirstErrorIndex();

		if (firstErrorIndex !== -1) {
			// Usar setTimeout para garantir que o DOM foi atualizado
			setTimeout(() => {
				const element = itemRefs.current[firstErrorIndex];

				if (element) {
					const offset = 120; // Offset para headers e espaçamento
					const elementTop = element.offsetTop;
					const targetPosition = elementTop - offset;

					window.scrollTo({
						top: Math.max(0, targetPosition),
						behavior: "smooth",
					});

					// Adicionar destaque visual temporário
					element.style.transition = "box-shadow 0.3s ease";
					element.style.boxShadow = "0 0 0 3px rgba(239, 68, 68, 0.3)";

					setTimeout(() => {
						element.style.boxShadow = "";
					}, 2000);
				}
			}, 100);
		}
	}, [findFirstErrorIndex]);

	// const scrollToItemError = useCallback((itemIndex: number) => {
	// 	setTimeout(() => {
	// 		const element = itemRefs.current[itemIndex];
	// 		if (element) {
	// 			const offset = 120; // Offset para headers e espaçamento
	// 			const elementTop = element.offsetTop;
	// 			const targetPosition = elementTop - offset;

	// 			window.scrollTo({
	// 				top: Math.max(0, targetPosition),
	// 				behavior: "smooth",
	// 			});

	// 			// Adicionar destaque visual temporário
	// 			element.style.transition = "box-shadow 0.3s ease";
	// 			element.style.boxShadow = "0 0 0 3px rgba(239, 68, 68, 0.3)";

	// 			setTimeout(() => {
	// 				element.style.boxShadow = "";
	// 			}, 2000);
	// 		}
	// 	}, 100);
	// }, []);

	const setItemRef = useCallback(
		(index: number) => (el: HTMLElement | null) => {
			itemRefs.current[index] = el;
		},
		[]
	);

	return {
		scrollToFirstError,
		setItemRef,
	};
}

// Função auxiliar para extrair campos com erro de um objeto de erro
function getErrorFields(errorObj: unknown, prefix = ""): string[] {
	const fields: string[] = [];

	if (!errorObj || typeof errorObj !== "object") {
		return fields;
	}

	for (const [key, value] of Object.entries(errorObj)) {
		const fieldPath = prefix ? `${prefix}.${key}` : key;

		if (value && typeof value === "object") {
			if ("message" in value) {
				// É um erro direto
				fields.push(fieldPath);
			} else {
				// É um objeto aninhado, continua a busca
				fields.push(...getErrorFields(value, fieldPath));
			}
		}
	}

	return fields;
}
