import { useCreateOrder } from "@/modules/sales-panel/hooks/order/use-create-order.hook";
import { orderInfoAtom } from "@/modules/sales-panel/states/order-info.state";
import { Button } from "@/shared/components/ui/button";
import { Link } from "@tanstack/react-router";
import { useAtomValue } from "jotai";
import { ArrowLeft, Plus, User } from "lucide-react";
import React from "react";

export const TerminalMobileHeader: React.FC<{
	orderId: number | null;
	createOrderMutation: ReturnType<typeof useCreateOrder>["createOrderMutation"];
}> = ({ orderId, createOrderMutation }) => {
	const orderInfo = useAtomValue(orderInfoAtom);

	console.log("orderInfo", orderInfo);
	console.log("orderId", orderId);

	return (
		<div className="w-full h-[80px] lg:hidden px-4 py-2 bg-white shadow-md rounded-[20px] flex items-center justify-between">
			<Button className="bg-mainColor shadow-md gap-2 flex text-white px-3 py-1 rounded-[15px]">
				<Link to="/" className="flex items-center gap-2">
					<ArrowLeft size={18} />
					Sair
				</Link>
			</Button>

			{orderId ? (
				<div className="flex flex-col items-end">
					<div className="flex items-center gap-2">
						<h2 className="text-base font-medium text-gray-700">
							Pedido <span className="font-bold">#{orderId}</span>
						</h2>
						<span className="bg-mainColor/10 border border-mainColor text-mainColor text-xs px-2 py-0.5 rounded-full">Em aberto</span>
					</div>

					{orderInfo?.customer || orderInfo?.cpfCnpj ? (
						<div className="flex items-center gap-1 mt-1">
							<User size={14} className="text-mainColor" />
							{orderInfo?.customer && <span className="text-xs text-gray-600">{orderInfo.customer}</span>}
							{orderInfo?.cpfCnpj && <span className="text-xs text-gray-500">({orderInfo.cpfCnpj})</span>}
						</div>
					) : (
						<span className="text-xs text-gray-500 mt-1">Cliente não informado</span>
					)}
				</div>
			) : (
				<Button
					onClick={() => createOrderMutation.mutate()}
					className="
bg-gradient-to-r from-[#227989] to-[#2fa2b5]
shadow-main
flex items-center gap-1 2xl:gap-2
text-white font-semibold
2xl:px-4 2xl:py-2
rounded-[15px]
transition-all duration-300
hover:scale-105 hover:shadow-lg
active:scale-100
"
				>
					<Plus size={18} />
					Adicionar venda
					<span className="text-xs bg-white text-[#227989] px-1 rounded">I</span>
				</Button>
			)}
		</div>
	);
};
