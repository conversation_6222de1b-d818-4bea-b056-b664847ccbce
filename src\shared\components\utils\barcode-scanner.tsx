import { BrowserMultiFormatReader } from "@zxing/browser";
import { AnimatePresence, motion } from "framer-motion";
import { AlertCircle, Camera, CheckCircle2, XCircleIcon } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";

import LogoMin from "@/shared/assets/logo/logo-mini.png";

interface BarcodeScannerProps {
	onDetected: (barcode: string) => void;
	onClose: () => void;
}

const BarcodeScanner: React.FC<BarcodeScannerProps> = ({ onDetected, onClose }) => {
	const videoRef = useRef<HTMLVideoElement>(null);
	const [detectedCode, setDetectedCode] = useState<string | null>(null);
	const [error, setError] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const codeReaderRef = useRef<BrowserMultiFormatReader | null>(null);

	useEffect(() => {
		const initializeCamera = async () => {
			try {
				const devices = await BrowserMultiFormatReader.listVideoInputDevices();

				if (devices.length === 0) {
					throw new Error("Nenhuma câmera encontrada");
				}

				const backCamera = devices.find(
					device => device.label.toLowerCase().includes("back") || device.label.toLowerCase().includes("traseira")
				);
				const activeDeviceId = backCamera ? backCamera.deviceId : devices[0].deviceId;

				codeReaderRef.current = new BrowserMultiFormatReader();
				// let      = 0;
				let lastCode = "";

				await codeReaderRef.current.decodeFromVideoDevice(activeDeviceId, videoRef.current!, (result, err) => {
					if (result) {
						const barcode = result.getText();

						if (barcode === lastCode) {
							// consecutiveReads++;
							// if (consecutiveReads >= 3) {
							setDetectedCode(barcode);
							// }
						} else {
							// consecutiveReads = 1;
							lastCode = barcode;
						}
					}
					if (err && !(err instanceof TypeError)) {
						console.warn("Erro de leitura:", err);
					}
				});

				setIsLoading(false);
			} catch (err) {
				setError(err instanceof Error ? err.message : "Erro ao inicializar câmera");
				setIsLoading(false);
			}
		};

		initializeCamera();

		return () => {
			if (codeReaderRef.current) {
			
				codeReaderRef.current = null;
			}
		};
	}, []);

	const handleConfirm = () => {
		if (detectedCode) {
			onDetected(detectedCode);
			onClose();
		}
	};

	const handleReject = () => {
		setDetectedCode(null);
	};

	if (error) {
		return (
			<div className="fixed inset-0 bg-black/95 flex items-center justify-center p-4">
				<div className="bg-white rounded-lg p-6 max-w-sm w-full text-center">
					<AlertCircle className="mx-auto mb-4 text-red-500" size={48} />
					<h2 className="text-xl font-bold mb-2">Erro</h2>
					<p className="text-gray-600 mb-4">{error}</p>
					<button onClick={onClose} className="bg-mainColor text-white px-6 py-2 rounded-lg hover:bg-opacity-90 transition-opacity">
						Fechar
					</button>
				</div>
			</div>
		);
	}

	return (
		<div className="fixed inset-0 bg-black overflow-hidden">
			<button
				type="button"
				onClick={onClose}
				className="absolute right-4 top-4 z-40 text-white bg-black/50 rounded-full p-2 hover:bg-black/70 transition-colors"
				aria-label="Fechar scanner"
			>
				<XCircleIcon size={24} />
			</button>

			<AnimatePresence>
				{isLoading && (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						exit={{ opacity: 0 }}
						className="absolute inset-0 flex items-center justify-center bg-black/90 z-50"
					>
						<motion.div animate={{ rotate: 360 }} transition={{ duration: 1, repeat: Infinity, ease: "linear" }}>
							<Camera size={48} className="text-mainColor" />
						</motion.div>
					</motion.div>
				)}
			</AnimatePresence>

			<video ref={videoRef} className="absolute inset-0 w-full h-full object-cover" muted autoPlay playsInline />

			<div className="pointer-events-none absolute inset-0 flex items-center justify-center">
				<img src={LogoMin} alt="Logo do Sistema" className="absolute top-6 left-1/2 -translate-x-1/2 z-40 h-14 w-auto" />

				<motion.div
					className="relative w-3/4 h-2/5 sm:w-1/2 sm:h-1/3"
					animate={{
						boxShadow: ["0 0 0 0px rgba(1, 151, 178, 0.2)", "0 0 0 20px rgba(1, 151, 178, 0)"],
					}}
					transition={{
						duration: 1.5,
						repeat: Infinity,
						ease: "easeOut",
					}}
				>
					<div className="absolute inset-0 border-4 border-transparent">
						<div className="absolute -top-2 -left-2 w-8 h-8 border-t-4 border-l-4 border-mainColor rounded-tl-md" />
						<div className="absolute -top-2 -right-2 w-8 h-8 border-t-4 border-r-4 border-mainColor rounded-tr-md" />
						<div className="absolute -bottom-2 -left-2 w-8 h-8 border-b-4 border-l-4 border-mainColor rounded-bl-md" />
						<div className="absolute -bottom-2 -right-2 w-8 h-8 border-b-4 border-r-4 border-mainColor rounded-br-md" />
					</div>

					<motion.div
						className="absolute left-0 right-0 h-0.5 bg-mainColor"
						initial={{ top: "0%" }}
						animate={{ top: ["0%", "100%"] }}
						transition={{
							duration: 2,
							repeat: Infinity,
							repeatType: "reverse",
							ease: "easeInOut",
						}}
					/>
				</motion.div>
			</div>

			<div className="pointer-events-none absolute inset-x-0 bottom-0 h-32 bg-gradient-to-t from-black to-transparent z-20" />

			<AnimatePresence>
				{detectedCode && (
					<motion.div
						initial={{ opacity: 0, y: 50 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: 50 }}
						className="absolute inset-x-0 bottom-20 flex flex-col items-center justify-center gap-4 z-50"
					>
						<div className="bg-black/80 backdrop-blur-sm text-white px-8 py-4 rounded-xl text-center max-w-sm mx-auto">
							<motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="mx-auto mb-3">
								<CheckCircle2 size={32} className="text-green-400 mx-auto" />
							</motion.div>
							<p className="text-lg font-semibold mb-2">Código detectado:</p>
							<p className="text-2xl font-bold text-mainColor mb-3">{detectedCode}</p>
							<p className="text-sm text-gray-300">Este é o código correto?</p>
						</div>
						<div className="flex gap-4">
							<motion.button
								whileHover={{ scale: 1.05 }}
								whileTap={{ scale: 0.95 }}
								onClick={handleConfirm}
								className="bg-mainColor text-white px-8 py-3 rounded-lg font-medium shadow-lg hover:bg-opacity-90 transition-colors"
							>
								Confirmar
							</motion.button>
							<motion.button
								whileHover={{ scale: 1.05 }}
								whileTap={{ scale: 0.95 }}
								onClick={handleReject}
								className="bg-red-500 text-white px-8 py-3 rounded-lg font-medium shadow-lg hover:bg-opacity-90 transition-colors"
							>
								Tentar novamente
							</motion.button>
						</div>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
};

export default BarcodeScanner;
