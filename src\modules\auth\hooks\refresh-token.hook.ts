import { useMutation } from "@tanstack/react-query";
import { useSet<PERSON>tom } from "jotai";
import Cookies from "js-cookie";
import { toast } from "sonner";
import { refreshTokenRequest } from "../api/refresh.request";
import { IRefreshTokenDTO } from "../dtos/refresh.dto";
import { authStateAtom } from "../states/auth.state";
import { useLogout } from "./logout.hook";

export const useRefreshTokenMutation = () => {
	const setAuthState = useSetAtom(authStateAtom);
	const { logout } = useLogout();

	const mutation = useMutation({
		mutationFn: async (data: IRefreshTokenDTO) => {
			const response = await refreshTokenRequest(data);
			if (response.success) return response.data;
			throw new Error(response.data.message);
		},
		onSuccess: res => {
			Cookies.set("access_token", res.data.access_token);
			Cookies.set("refresh_token", res.data.refresh_token);

			setAuthState({
				isAuthenticated: true,
			});

			toast.success("Token renovado com sucesso!");
		},
		onError: () => {
			toast.error("Falha ao renovar token. Faça login novamente.");
			logout();
		},
		retry: false,
	});

	return mutation;
};
