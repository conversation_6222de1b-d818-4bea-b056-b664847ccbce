import { useCallback } from "react";
import { Control, FieldPath, FieldValues, useController } from "react-hook-form";
import { formatPriceForInput } from "../utils/price-formatter";

interface UsePriceFieldProps<T extends FieldValues> {
	name: FieldPath<T>;
	control: Control<T>;
	rules?: object;
	defaultValue?: number;
}

export function usePriceField<T extends FieldValues>({ name, control, rules, defaultValue = 0 }: UsePriceFieldProps<T>) {
	const {
		field: { value, onChange, ...fieldProps },
		fieldState: { error },
	} = useController({
		name,
		control,
		rules,
		defaultValue: defaultValue as T[keyof T],
	});

	const displayValue = formatPriceForInput(value);

	const handleChange = useCallback(
		(_formattedValue: string, numericValue: number | undefined) => {
			onChange(numericValue ?? 0);
		},
		[onChange]
	);

	const setValue = useCallback(
		(newValue: number) => {
			onChange(newValue);
		},
		[onChange]
	);

	return {
		...fieldProps,
		value: displayValue,
		numericValue: value,
		onChange: handleChange,
		setValue,
		error,
		isValid: !error && value > 0,
	};
}
