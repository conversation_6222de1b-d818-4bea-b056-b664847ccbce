import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";

import { InvoiceConfig } from "../../components/invoice-config-modal";
import { ORDERS_ENDPOINTS } from "../endpoints";

export const sendForBillingRequest = async (id: number, config?: InvoiceConfig): Promise<ApiResponse<IGlobalMessageReturn>> => {
	return createRequest({
		path: ORDERS_ENDPOINTS.SEND_FOR_BILLING(id),
		method: "POST",
		body: config,
	});
};
