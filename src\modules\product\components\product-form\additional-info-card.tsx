import { Textarea } from "@/shared/components/ui/textarea";
import { Info } from "lucide-react";
import { JSX } from "react";
import { UseFormReturn } from "react-hook-form";
import { ProductFormData } from "../../validators/product/product-form.schema";
import { SuppliersSelect } from "./suppliers-select";

interface AdditionalInfoCardProps {
	methods: UseFormReturn<ProductFormData>;
}

export const AdditionalInfoCard: React.FC<AdditionalInfoCardProps> = ({ methods }): JSX.Element => {
	return (
		<div className="bg-gray-50/50 rounded-lg p-4 border border-gray-100">
			<div className="flex items-center gap-2 mb-3">
				<div className="p-1.5 bg-mainColor/10 rounded-lg">
					<Info size={16} className="text-mainColor" />
				</div>
				<label className="text-sm font-medium text-gray-700">Informações Adicionais</label>
			</div>

			<div className="space-y-4">
				<SuppliersSelect
					required
					value={methods.watch("supplierId")}
					onChange={value => methods.setValue("supplierId", value)}
					error={methods.formState.errors.supplierId?.message as string}
				/>

				<div>
					<label htmlFor="description" className="flex items-center gap-1.5 text-sm font-semibold text-gray-700 mb-1.5">
						<span className="text-gray-500">
							<Info size={14} />
						</span>
						Descrição
					</label>

					<Textarea
						id="description"
						className="border border-gray-200 rounded-lg w-full px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-mainColor/50 transition-all duration-200 bg-white/50 hover:border-gray-300 h-24 resize-none"
						placeholder="Ex: Descrição do produto..."
						{...methods.register("description")}
					/>
				</div>
			</div>
		</div>
	);
};
