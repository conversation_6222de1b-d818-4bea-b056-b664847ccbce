export interface IFindAllInvoicesParams {
  page: number;
  limit: number;
  filter?: string;
  issueDate?: string;
  status?: 'autorizado' | 'rejeitado' | 'inutilizado' | 'registrado' | 'cancelado';
  type?: 0 | 1;
}

export interface IInvoiceEvent {
  id: string;
  date: string;
  reason: string;
}

export interface IInvoice {
  id: number;
  type: 0 | 1;
  status: string;
  issueDate: string;
  key: string;
  sequence: number;
  orderId: number;
  supplier: string;
  events: IInvoiceEvent[];
}

export interface IFindAllInvoicesResponse {
  data: IInvoice[];
  total: number;
}

export interface IInvalidateInvoiceResponse {
  message: string;
}
