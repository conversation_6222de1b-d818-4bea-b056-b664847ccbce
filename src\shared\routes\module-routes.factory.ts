import { AnyRoute, createRoute, RouteComponent } from "@tanstack/react-router";

export interface ModuleRouteConfig<T extends { id: string; path: string }> {
	moduleActivated: boolean;
	subItems?: T[];
}

export interface ModuleNavigationHelper {
	shouldNavigateDirectly: boolean;
	targetPath: string;
	hasMultipleSubItems: boolean;
}

export interface ModuleSubRoute<T extends { id: string; path: string }> {
	id: T["id"];
	component: RouteComponent;
}

export function getModuleNavigationHelper<T extends { id: string; path: string }>(moduleConfig: ModuleRouteConfig<T>): ModuleNavigationHelper {
	const subItemsCount = moduleConfig.subItems?.length ?? 0;
	const hasMultipleSubItems = subItemsCount > 1;
	const shouldNavigateDirectly = subItemsCount <= 1;

	const targetPath = subItemsCount === 1 ? moduleConfig.subItems![0].path : (moduleConfig.subItems?.[0]?.path ?? "");

	return {
		shouldNavigateDirectly,
		targetPath,
		hasMultipleSubItems,
	};
}

export function createModuleRoutes<T extends { id: string; path: string }>(
	moduleConfig: ModuleRouteConfig<T>,
	subRoutes: ModuleSubRoute<T>[],
	getParentRoute: () => AnyRoute
): ReturnType<typeof createRoute>[] {
	if (!moduleConfig.moduleActivated) {
		return [];
	}

	const navigationHelper = getModuleNavigationHelper(moduleConfig);
	if (!moduleConfig.subItems || moduleConfig.subItems.length <= 1) {
		return [
			createRoute({
				component: subRoutes[0]?.component,
				path: navigationHelper.targetPath,
				getParentRoute,
			}),
		];
	}

	return subRoutes.map(routeDef => {
		const config = moduleConfig.subItems!.find(item => item.id === routeDef.id);
		return createRoute({
			component: routeDef.component,
			path: config?.path ?? "",
			getParentRoute,
		});
	});
}

export function shouldNavigateDirectly(subItems?: Array<{ id: string; path: string }>): boolean {
	const subItemsCount = subItems?.length ?? 0;
	return subItemsCount <= 1;
}
