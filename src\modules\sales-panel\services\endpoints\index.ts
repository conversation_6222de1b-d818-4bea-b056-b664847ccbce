export const SALES_PANEL_PATHS = {
	CREATE_ORDER: "/order/create",
	ADD_PRODUCT: ({ orderId }: { orderId: number }) => `/order/${orderId}/item-quantity`,
	ADD_PAYMENT: ({ orderId }: { orderId: number }) => `/order/${orderId}/add-payment`,
	REMOVE_PAYMENT: ({ orderId, paymentId }: { orderId: number; paymentId: number }) => `/order/${orderId}/remove-payment/${paymentId}`,
	FINISH_ORDER: ({ orderId }: { orderId: number }) => `/order/${orderId}/finish`,
	FIND_ORDER_BY_ID: ({ orderId }: { orderId: number }) => `/order/${orderId}/find`,
	UPDATE_ORDER: ({ orderId }: { orderId: number }) => `/order/${orderId}/update`,
	DELETE_ORDER: ({ orderId }: { orderId: number }) => `/order/${orderId}/delete`,
	UPDATE_ITEM: ({ orderId }: { orderId: number }) => `/order/${orderId}/update-item`,
	REMOVE_ITEM: ({ orderId, itemId }: { orderId: number; itemId: number }) => `/order/${orderId}/remove-item/${itemId}`,
	GET_FISCAL_CUPOM: ({ orderId }: { orderId: number }) => `/order/${orderId}/coupon`,
	GET_ALL_PENDINGS: ({ page, limit, orderId, customer }: { page: number; limit: number; orderId: number; customer: string }) =>
		`/order/find-all-opened/${page}/${limit}/?orderId=${orderId}&customer=${customer}`,
} as const;

export const SOCKET_ORDER_CONNECTION = {
	CONNECT: ({ orderId }: { orderId: number }) => {
		const baseUrl = import.meta.env.VITE_API_BASE_URL;
		// Remove trailing slash to avoid double slashes
		const cleanUrl = baseUrl.endsWith("/") ? baseUrl.slice(0, -1) : baseUrl;
		return `${cleanUrl}/?orderId=${orderId}`;
	},
};
