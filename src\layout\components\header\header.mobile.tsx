import { motion } from "framer-motion";
import { Bell, LogOut, User } from "lucide-react";
import { useUserProfile } from "@/modules/auth/hooks/user-profile.hook";
import { useLogout } from "@/modules/auth/hooks/logout.hook";
import { Popover, PopoverContent, PopoverTrigger } from "@/shared/components/ui/popover";

interface HeaderMobileProps {
	readonly isExpanded: boolean;
	readonly isExpandedDuringScroll: boolean;
	readonly onExpandRequest: () => void;
}

export default function HeaderMobile({ isExpanded, isExpandedDuringScroll, onExpandRequest }: HeaderMobileProps) {
	const headerVariants = {
		expanded: {
			width: "100vw",
			height: "auto",
			position: "fixed" as const,
			top: 0,
			right: 0,
			zIndex: 1000,
			borderRadius: "0px",
			boxShadow: "none",
			transformOrigin: "100% 50%",
			transformBox: "border-box" as const,
			transition: { duration: 0.3 },
		},

		expandedScroll: {
			width: "100vw",
			height: "auto",
			position: "fixed" as const,
			top: 0,
			right: 0,
			zIndex: 1000,
			transformOrigin: "100% 50%",
			transformBox: "border-box" as const,
			transition: { duration: 0.3 },
		},
		pill: {
			width: "160px",
			height: "48px",
			borderRadius: "9999px",
			position: "fixed" as const,
			top: "13px",
			zIndex: 1000,
			right: "13px",
			boxShadow: "0 4px 10px rgba(0, 0, 0, 0.20)",
			transformOrigin: "100% 50%",
			transformBox: "border-box" as const,
			transition: { duration: 0.3 },
		},
	};

	let currentVariant: keyof typeof headerVariants;
	if (isExpandedDuringScroll) {
		currentVariant = "expandedScroll";
	} else if (isExpanded) {
		currentVariant = "expanded";
	} else {
		currentVariant = "pill";
	}

	return (
		<motion.header initial="expanded" animate={currentVariant} variants={headerVariants} className="overflow-hidden">
			{currentVariant === "pill" ? (
				<PillHeaderContent onExpandRequest={onExpandRequest} />
			) : (
				<FullHeaderContent isExpandedScroll={currentVariant === "expandedScroll"} />
			)}
		</motion.header>
	);
}

function FullHeaderContent({ isExpandedScroll }: Readonly<{ isExpandedScroll?: boolean }>) {
	const { profile } = useUserProfile();
	const { logout } = useLogout();

	const handleLogout = () => {
		logout();
	};

	return (
		<div
			className={`flex items-center  justify-between bg-white rounded-lg px-4 py-4 m-1 ${isExpandedScroll ? "shadow-[0_4px_12px_rgba(0,0,0,0.20)]" : "shadow-none"}`}
		>
			<div className="flex items-center space-x-4">
				<h1 className="text-mainColor text-xl font-bold">LMP</h1>
				<div>
					<p className="text-sm text-gray-500">Bem vindo de volta,</p>
					<p className="text-base font-bold text-gray-700">{profile?.name || "Usuário"}</p>
				</div>
			</div>
			<div className="flex items-center space-x-4">
				<Bell className="text-gray-500 hover:text-mainColor cursor-pointer" />
				<Popover>
					<PopoverTrigger asChild>
						<img
							src="https://png.pngtree.com/thumb_back/fh260/background/20230615/pngtree-man-wearing-a-pair-of-yellow-sunglasses-in-front-of-a-image_2898170.jpg"
							alt="Avatar"
							className="w-12 h-12 rounded-full object-cover cursor-pointer hover:ring-2 hover:ring-mainColor/30 transition-all duration-200"
						/>
					</PopoverTrigger>
					<PopoverContent className="w-[200px] mr-2 rounded-[15px] p-0">
						<div className="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
							<div className="flex items-center gap-2">
								<div className="w-8 h-8 rounded-full overflow-hidden ring-1 ring-mainColor">
									<img
										src="https://png.pngtree.com/thumb_back/fh260/background/20230615/pngtree-man-wearing-a-pair-of-yellow-sunglasses-in-front-of-a-image_2898170.jpg"
										alt="Avatar"
										className="w-full h-full object-cover"
									/>
								</div>
								<div>
									<h3 className="font-medium text-xs text-gray-800">{profile?.name || "Usuário"}</h3>
									<p className="text-xs text-gray-500"><EMAIL></p>
								</div>
							</div>
						</div>
						<div className="py-1">
							<button
								className="w-full px-3 py-2 text-sm text-gray-400 cursor-not-allowed flex items-center gap-2"
								disabled
								title="Funcionalidade em desenvolvimento"
							>
								<User size={14} className="text-gray-400" />
								Meu Perfil
							</button>
							<button
								onClick={handleLogout}
								className="w-full px-3 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-600 flex items-center gap-2 transition-colors duration-200"
							>
								<LogOut size={14} className="text-red-500" />
								Sair
							</button>
						</div>
					</PopoverContent>
				</Popover>
			</div>
		</div>
	);
}

interface PillProps {
	readonly onExpandRequest: () => void;
}

function PillHeaderContent({ onExpandRequest }: PillProps) {
	const { profile } = useUserProfile();
	const { logout } = useLogout();

	const handleLogout = () => {
		logout();
	};

	return (
		<div className="w-full h-full bg-white flex items-center justify-center">
			<div className="flex items-center space-x-2">
				<button type="button" onClick={onExpandRequest} className="flex items-center space-x-2">
					<h1 className="text-mainColor text-md font-bold">LMP</h1>
					<Bell className="w-6 h-6 text-gray-500 hover:text-mainColor cursor-pointer" />
				</button>
				<Popover>
					<PopoverTrigger asChild>
						<img
							src="https://png.pngtree.com/thumb_back/fh260/background/20230615/pngtree-man-wearing-a-pair-of-yellow-sunglasses-in-front-of-a-image_2898170.jpg"
							alt="Avatar"
							className="w-9 h-9 rounded-full object-cover cursor-pointer hover:ring-2 hover:ring-mainColor/30 transition-all duration-200"
						/>
					</PopoverTrigger>
					<PopoverContent className="w-[200px] mr-2 rounded-[15px] p-0">
						<div className="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
							<div className="flex items-center gap-2">
								<div className="w-8 h-8 rounded-full overflow-hidden ring-1 ring-mainColor">
									<img
										src="https://png.pngtree.com/thumb_back/fh260/background/20230615/pngtree-man-wearing-a-pair-of-yellow-sunglasses-in-front-of-a-image_2898170.jpg"
										alt="Avatar"
										className="w-full h-full object-cover"
									/>
								</div>
								<div>
									<h3 className="font-medium text-xs text-gray-800">{profile?.name || "Usuário"}</h3>
									<p className="text-xs text-gray-500"><EMAIL></p>
								</div>
							</div>
						</div>
						<div className="py-1">
							<button
								className="w-full px-3 py-2 text-sm text-gray-400 cursor-not-allowed flex items-center gap-2"
								disabled
								title="Funcionalidade em desenvolvimento"
							>
								<User size={14} className="text-gray-400" />
								Meu Perfil
							</button>
							<button
								onClick={handleLogout}
								className="w-full px-3 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-600 flex items-center gap-2 transition-colors duration-200"
							>
								<LogOut size={14} className="text-red-500" />
								Sair
							</button>
						</div>
					</PopoverContent>
				</Popover>
			</div>
		</div>
	);
}
