import { CardLMPContainer } from "@/shared/components/custom/card";
import { BarChart3 } from "lucide-react";
import { motion } from "framer-motion";
import { IDashboardChartsData, PeriodType } from "../../types/dashboard.types";
import { SalesChart } from "./sales-chart";
import { ProductsChart } from "./products-chart";
import { ClientsChart } from "./clients-chart";
import React from "react";

interface IChartsSectionProps {
	selectedPeriod: PeriodType;
	onPeriodChange: (period: PeriodType) => void;
	chartsData: IDashboardChartsData;
}

interface IPeriodButtonProps {
	period: PeriodType;
	label: string;
	isActive: boolean;
	onClick: (period: PeriodType) => void;
}

interface IPeriodSelectorProps {
	selectedPeriod: PeriodType;
	onPeriodChange: (period: PeriodType) => void;
}

const PeriodButton = React.memo(({ period, label, isActive, onClick }: IPeriodButtonProps) => (
	<button
		className={`
			px-2.5 py-1.5 font-medium text-xs sm:text-sm rounded-lg transition-all duration-200
			focus:outline-none focus:ring-2 focus:ring-mainColor/30
			${isActive ? "bg-mainColor text-white shadow-sm" : "text-gray-600 hover:text-gray-800 hover:bg-gray-100 bg-white border border-gray-200"}
		`}
		onClick={() => onClick(period)}
		aria-pressed={isActive}
		role="tab"
	>
		{label}
	</button>
));

PeriodButton.displayName = "PeriodButton";

const CompactPeriodSelector = React.memo(({ selectedPeriod, onPeriodChange }: IPeriodSelectorProps) => {
	const periods: { period: PeriodType; label: string }[] = [
		{ period: "diario", label: "Diário" },
		{ period: "semanal", label: "Semanal" },
		{ period: "mensal", label: "Mensal" },
		{ period: "anual", label: "Anual" },
	];

	return (
		<div className="flex items-center gap-2 sm:gap-3">
			<span className="hidden sm:inline text-sm font-medium text-gray-700">Período:</span>
			<div
				className="flex gap-1 p-1 bg-gray-50/50 rounded-xl border border-gray-200/50 shadow-sm"
				role="tablist"
				aria-label="Seleção de período"
			>
				{periods.map(({ period, label }) => (
					<PeriodButton key={period} period={period} label={label} isActive={selectedPeriod === period} onClick={onPeriodChange} />
				))}
			</div>
		</div>
	);
});

CompactPeriodSelector.displayName = "CompactPeriodSelector";

export const ChartsSection = React.memo(({ selectedPeriod, onPeriodChange, chartsData }: IChartsSectionProps) => {
	return (
		<CardLMPContainer
			icon={<BarChart3 size={22} className="text-mainColor" />}
			title="Análise de Dados"
			description="Visualize os dados do seu negócio em diferentes perspectivas"
			actions={<CompactPeriodSelector selectedPeriod={selectedPeriod} onPeriodChange={onPeriodChange} />}
		>
			<motion.div
				key={selectedPeriod}
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ duration: 0.3, ease: "easeOut" }}
				className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6"
			>
				<div className="order-1">
					<SalesChart data={chartsData.vendasMensais} />
				</div>
				<div className="order-2">
					<ProductsChart data={chartsData.produtosMaisVendidos} />
				</div>
				<div className="order-3 xl:col-span-2">
					<ClientsChart data={chartsData.clientesPorRegiao} />
				</div>
			</motion.div>
		</CardLMPContainer>
	);
});

ChartsSection.displayName = "ChartsSection";
