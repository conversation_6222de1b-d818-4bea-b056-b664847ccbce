/**
 * Utilitário para formatação e limpeza de códigos NCM
 */
export class NcmFormatterUtil {
	/**
	 * Remove pontos, traços e outros caracteres especiais do NCM,
	 * mantendo apenas os números
	 * @param ncm - Código NCM com ou sem formatação
	 * @returns NCM apenas com números
	 */
	public static cleanNcm(ncm: string): string {
		if (!ncm || typeof ncm !== 'string') {
			return '';
		}

		// Remove todos os caracteres que não são números
		return ncm.replace(/\D/g, '');
	}

	/**
	 * Formata o NCM para exibição com pontos
	 * @param ncm - Código NCM apenas com números
	 * @returns NCM formatado (ex: 1234.56.78)
	 */
	public static formatNcm(ncm: string): string {
		if (!ncm || typeof ncm !== 'string') {
			return '';
		}

		const cleanedNcm = this.cleanNcm(ncm);
		
		// Se não tem 8 dígitos, retorna como está
		if (cleanedNcm.length !== 8) {
			return cleanedNcm;
		}

		// Formata como XXXX.XX.XX
		return `${cleanedNcm.slice(0, 4)}.${cleanedNcm.slice(4, 6)}.${cleanedNcm.slice(6, 8)}`;
	}

	/**
	 * Valida se o NCM tem o formato correto (8 dígitos)
	 * @param ncm - Código NCM
	 * @returns true se válido
	 */
	public static isValidNcm(ncm: string): boolean {
		if (!ncm || typeof ncm !== 'string') {
			return false;
		}

		const cleanedNcm = this.cleanNcm(ncm);
		return cleanedNcm.length === 8 && /^\d{8}$/.test(cleanedNcm);
	}
}
