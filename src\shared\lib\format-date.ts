import { format, isValid, parse, parseISO } from "date-fns";
import { ptBR } from "date-fns/locale";

export const formatDate = (dateString: string | undefined, dateFormat: string = "dd/MM/yyyy"): string => {
	if (!dateString) return "-";

	try {
		if (dateString.match(/^\d{2}\/\d{2}\/\d{4}/)) {
			const datePart = dateString.split(" ")[0];
			if (dateFormat !== "dd/MM/yyyy") {
				const parsedDate = parse(datePart, "dd/MM/yyyy", new Date());
				if (isValid(parsedDate)) {
					return format(parsedDate, dateFormat, { locale: ptBR });
				}
			} else {
				return datePart;
			}
		}

		const date = parseISO(dateString);

		if (!isValid(date)) {
			const parsedDate = new Date(dateString);
			if (!isValid(parsedDate)) {
				return "-";
			}
			return format(parsedDate, dateFormat, { locale: ptBR });
		}

		return format(date, dateFormat, { locale: ptBR });
	} catch (error) {
		console.warn("Erro ao formatar data:", error);
		return "-";
	}
};

export const formatDateTime = (dateString: string | undefined): string => {
	return formatDate(dateString, "dd/MM/yyyy HH:mm");
};
