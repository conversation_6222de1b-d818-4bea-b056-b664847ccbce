import { AnimatePresence, motion } from "framer-motion";
import React from "react";

interface EnhancedErrorMessageProps {
	error?: string;
	fieldName?: string;
	className?: string;
}

export const EnhancedErrorMessage: React.FC<EnhancedErrorMessageProps> = ({ error, className = "" }) => {
	if (!error) return null;

	return (
		<AnimatePresence>
			<motion.div
				initial={{ opacity: 0, y: -5 }}
				animate={{ opacity: 1, y: 0 }}
				exit={{ opacity: 0, y: -5 }}
				transition={{ duration: 0.2 }}
				className={`mt-1 ${className}`}
			>
				<span className="text-xs text-red-600 font-medium">{error}</span>
			</motion.div>
		</AnimatePresence>
	);
};

interface FieldErrorWrapperProps {
	children: React.ReactNode;
	error?: string;
	fieldName?: string;
	hasError?: boolean;
}

export const FieldErrorWrapper: React.FC<FieldErrorWrapperProps> = ({ children, error, fieldName, hasError = false }) => {
	return (
		<div className={`relative ${hasError ? "pb-1" : ""}`}>
			<div className={hasError ? "ring-2 ring-red-200 rounded-md" : ""}>{children}</div>
			<EnhancedErrorMessage error={error} fieldName={fieldName} />
		</div>
	);
};
