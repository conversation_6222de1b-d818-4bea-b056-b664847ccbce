

import { buildQueryParams } from "@/shared/lib/build-query-params";
import { IFindAllOrdersParams } from "../../dtos/find-all-orders.dto";
import { IFindAllInvoicesParams } from "../../dtos/invoice.dto";

export const ORDERS_ENDPOINTS = {
	FIND_ALL: (params: IFindAllOrdersParams) => {
		const { page, limit, ...rest } = params;
		const base = `/order/find-all/${page}/${limit}`;
		return buildQueryParams(base, rest);
	},
  INVOICE_FIND_ALL: (params: IFindAllInvoicesParams) => {
	const { page, limit, ...rest } = params;
	const base = `/invoice/find-all/${page}/${limit}`;
	return buildQueryParams(base, rest);
  },
  INVOICE_INVALIDATE: (id: number) => `/invoice/${id}/invalidate`,
	FIND_DETAILED_BY_ID: (id: number) => `/order/find-detailed-by-id/${id}`,
	SEND_FOR_BILLING: (id: number) => `/order/${id}/send-for-billing`,
	COUPON: (id: number) => `/order/${id}/coupon`,
};
