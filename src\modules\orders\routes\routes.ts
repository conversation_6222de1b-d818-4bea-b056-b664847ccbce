import { home } from "@/shared/routes/dashboard.route";
import { ModuleSubRoute, createModuleRoutes } from "@/shared/routes/module-routes.factory";
import { ORDERS_CONFIG, ORDERS_SUBITEM_IDS } from "../data/orders-config";
import { OrdersListPage } from "../pages/orders-list";

const ordersSubRoutes: ModuleSubRoute<NonNullable<typeof ORDERS_CONFIG.subItems>[number]>[] = [
	{ id: ORDERS_SUBITEM_IDS.ALL_ORDERS, component: OrdersListPage },
];

export const ordersRoutes = createModuleRoutes(
	{
		...ORDERS_CONFIG,
		subItems: ORDERS_CONFIG.subItems ?? [],
	},
	ordersSubRoutes,
	() => home
);
