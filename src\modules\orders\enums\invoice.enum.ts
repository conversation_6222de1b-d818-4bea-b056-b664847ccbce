export enum InvoiceModelEnum {
	NFE = 55,
	NFCE = 65,
}

export enum InvoiceFinalEnum {
	NO = 0,
	FINALCUSTOMER = 1,
}

export enum InvoiceBuyerPresenceEnum {
	NA = 0,
	InPerson = 1,
	Online = 2,
	Teleservice = 3,
	Delivery = 4,
	OutsideStore = 5,
	Other = 9,
}

// Dados para exibição nos selects
export const InvoiceModelData = [
	{ value: InvoiceModelEnum.NFE, label: "NF-e (Nota Fiscal Eletrônica)", description: "Modelo 55" },
	{ value: InvoiceModelEnum.NFCE, label: "NFC-e (Nota Fiscal de Consumidor Eletrônica)", description: "Modelo 65" },
];

export const InvoiceFinalData = [
	{ value: InvoiceFinalEnum.NO, label: "Não", description: "Operação não destinada a consumidor final" },
	{ value: InvoiceFinalEnum.FINALCUSTOMER, label: "Sim", description: "Operação destinada a consumidor final" },
];

export const InvoiceBuyerPresenceData = [
	{ value: InvoiceBuyerPresenceEnum.NA, label: "Não se aplica", description: "Não se aplica" },
	{ value: InvoiceBuyerPresenceEnum.InPerson, label: "Presencial", description: "Operação presencial" },
	{ value: InvoiceBuyerPresenceEnum.Online, label: "Internet", description: "Operação não presencial, pela Internet" },
	{ value: InvoiceBuyerPresenceEnum.Teleservice, label: "Teleatendimento", description: "Operação não presencial, teleatendimento" },
	{ value: InvoiceBuyerPresenceEnum.Delivery, label: "Entrega em domicílio", description: "NFC-e em operação com entrega a domicílio" },
	{ value: InvoiceBuyerPresenceEnum.OutsideStore, label: "Fora do estabelecimento", description: "Operação presencial, fora do estabelecimento" },
	{ value: InvoiceBuyerPresenceEnum.Other, label: "Outros", description: "Outros" },
];
