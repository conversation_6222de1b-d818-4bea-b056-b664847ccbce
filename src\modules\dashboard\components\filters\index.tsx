import { DatePickerInput } from "@/shared/components/custom/calendar-input";
import { CardLMPContainer } from "@/shared/components/custom/card";
import { Button } from "@/shared/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";
import { Calendar, Filter, RefreshCw } from "lucide-react";
import { Controller, FormProvider, UseFormReturn } from "react-hook-form";
import { IDashboardFilters } from "../../types/dashboard.types";

interface IDashboardFiltersSectionProps {
	methods: UseFormReturn<IDashboardFilters>;
}

export const DashboardFiltersSection = ({ methods }: IDashboardFiltersSectionProps) => {
	const { reset, control } = methods;

	const handleReset = () => {
		reset({
			periodo: "ultimo-mes",
			categoria: "todos",
			dataInicio: "",
			dataFim: "",
		});
	};

	const handleRefresh = () => {
		console.log("Atualizando dados do dashboard...");
	};

	return (
		<CardLMPContainer
			icon={<Filter size={22} className="text-gray-500" />}
			title="Filtros"
			description="Utilize os filtros abaixo para personalizar sua visualização."
			actions={
				<Button
					className="
						hidden md:flex items-center gap-2
						bg-mainColor text-white px-4 py-2
						rounded-[10px] h-[45px] text-sm font-medium
						hover:bg-mainColor/90 transition-colors shadow-sm
					"
					onClick={handleRefresh}
				>
					<RefreshCw size={18} />
					Atualizar
				</Button>
			}
		>
			<FormProvider {...methods}>
				<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
					{/* Período */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-gray-700 flex items-center gap-2">
							<Calendar size={16} className="text-gray-500" />
							Período
						</label>
						<Controller
							name="periodo"
							control={control}
							render={({ field }) => (
								<Select value={field.value} onValueChange={field.onChange}>
									<SelectTrigger className="h-[45px] rounded-[10px]">
										<SelectValue placeholder="Selecione o período" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="ultimo-mes">Último mês</SelectItem>
										<SelectItem value="ultimos-3-meses">Últimos 3 meses</SelectItem>
										<SelectItem value="ultimo-ano">Último ano</SelectItem>
										<SelectItem value="personalizado">Personalizado</SelectItem>
									</SelectContent>
								</Select>
							)}
						/>
					</div>

					{/* Categoria */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-gray-700 flex items-center gap-2">
							<Filter size={16} className="text-gray-500" />
							Categoria
						</label>
						<Controller
							name="categoria"
							control={control}
							render={({ field }) => (
								<Select value={field.value} onValueChange={field.onChange}>
									<SelectTrigger className="h-[45px] rounded-[10px]">
										<SelectValue placeholder="Selecione a categoria" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="todos">Todos os produtos</SelectItem>
										<SelectItem value="limpeza-domestica">Limpeza doméstica</SelectItem>
										<SelectItem value="limpeza-industrial">Limpeza industrial</SelectItem>
									</SelectContent>
								</Select>
							)}
						/>
					</div>

					{/* Data Início */}
					<div className="space-y-2">
						<Controller
							name="dataInicio"
							control={control}
							render={({ field }) => (
								<DatePickerInput
									field={field}
									label="Data Início"
									placeholder="Selecione a data"
									className="w-full"
									inputDateClassName="w-full h-[45px] rounded-[10px] border-gray-300"
									labelClassName="text-sm font-medium text-gray-700"
								/>
							)}
						/>
					</div>

					{/* Data Fim */}
					<div className="space-y-2">
						<Controller
							name="dataFim"
							control={control}
							render={({ field }) => (
								<DatePickerInput
									field={field}
									label="Data Fim"
									placeholder="Selecione a data"
									className="w-full"
									inputDateClassName="w-full h-[45px] rounded-[10px] border-gray-300"
									labelClassName="text-sm font-medium text-gray-700"
								/>
							)}
						/>
					</div>
				</div>

				{/* Botões Mobile */}
				<div className="flex md:hidden gap-2 mt-4">
					<Button variant="outline" onClick={handleReset} className="flex-1 h-[45px] rounded-[10px]">
						Limpar
					</Button>
					<Button onClick={handleRefresh} className="flex-1 h-[45px] rounded-[10px] text-white bg-mainColor hover:bg-mainColor/90">
						<RefreshCw size={18} className="mr-2" />
						Atualizar
					</Button>
				</div>
			</FormProvider>
		</CardLMPContainer>
	);
};
