import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";

import { motion } from "framer-motion";
import { Users } from "lucide-react";
import { useForm } from "react-hook-form";
import { useCreateGroup } from "../../hooks/group/create-group.hook";
import { ICreateGroupDto } from "../../dtos/group/create-group.dto";

interface CreateGroupModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export const CreateGroupModal = ({ isOpen, onClose }: CreateGroupModalProps) => {
	const {
		register,
		handleSubmit,
		formState: { errors, isSubmitting },
		reset,
	} = useForm<ICreateGroupDto>();

	const { createGroup, isLoading } = useCreateGroup();

	const onSubmit = async (data: ICreateGroupDto) => {
		createGroup(data);
		reset();
		onClose();
	};

	return (
		<OverlayContainer isVisible={isOpen} onClose={onClose}>
			<motion.div
				onClick={e => e.stopPropagation()}
				initial={{ opacity: 0, scale: 0.95 }}
				animate={{ opacity: 1, scale: 1 }}
				exit={{ opacity: 0, scale: 0.95 }}
				transition={{ duration: 0.2 }}
				className="bg-white rounded-[15px] w-full max-w-md mx-4 sm:mx-0 relative shadow-lg overflow-hidden p-6"
			>
				<div className="flex items-center gap-3 mb-4">
					<div className="bg-mainColor/10 p-2 rounded-full">
						<Users size={24} className="text-mainColor" />
					</div>
					<h2 className="text-lg font-semibold text-gray-800">Novo Grupo</h2>
				</div>
				<form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4">
					<div>
						<label className="block text-sm font-medium text-gray-600 mb-1">
							Nome do grupo <span className="text-red-500">*</span>
						</label>
						<Input
							type="text"
							placeholder="Digite o nome do grupo"
							{...register("name", { required: "Nome é obrigatório" })}
							className="w-full h-[45px] rounded-[10px]"
							autoFocus
						/>
						{errors.name && <span className="text-xs text-red-500 mt-1 block">{errors.name.message}</span>}
					</div>

					<div className="flex justify-end gap-3 mt-2">
						<Button type="button" onClick={onClose} className="bg-gray-200 hover:bg-gray-300 text-gray-700">
							Cancelar
						</Button>
						<Button type="submit" className="bg-mainColor text-white hover:bg-mainColor/90" disabled={isSubmitting || isLoading}>
							{isLoading ? "Salvando..." : "Salvar"}
						</Button>
					</div>
				</form>
			</motion.div>
		</OverlayContainer>
	);
};
