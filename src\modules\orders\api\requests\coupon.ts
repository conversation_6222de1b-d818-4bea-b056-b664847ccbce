import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { ORDERS_ENDPOINTS } from "../endpoints";

export const getCouponRequest = async (id: number): Promise<ApiResponse<File>> => {
	return createRequest({
		path: ORDERS_ENDPOINTS.COUPON(id),
		method: "GET",
		responseType: "blob", // Para receber arquivo PDF
	});
};
