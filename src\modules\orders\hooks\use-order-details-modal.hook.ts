import { useState, useCallback } from "react";

export interface IUseOrderDetailsModalReturn {
	isOpen: boolean;
	orderId: number | null;
	openModal: (orderId: number) => void;
	closeModal: () => void;
}

export const useOrderDetailsModal = (): IUseOrderDetailsModalReturn => {
	const [isOpen, setIsOpen] = useState(false);
	const [orderId, setOrderId] = useState<number | null>(null);

	const openModal = useCallback((id: number) => {
		setOrderId(id);
		setIsOpen(true);
	}, []);

	const closeModal = useCallback(() => {
		setIsOpen(false);
		setOrderId(null);
	}, []);

	return {
		isOpen,
		orderId,
		openModal,
		closeModal,
	};
};
