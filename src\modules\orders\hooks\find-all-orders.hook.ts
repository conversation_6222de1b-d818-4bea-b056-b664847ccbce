import { useQuery } from "@tanstack/react-query";
import { findAllOrdersRequest } from "../api/requests/find-all";
import { ORDERS_QUERY_KEYS } from "../data/query-keys";
import { IFindAllOrdersParams } from "../dtos/find-all-orders.dto";

export const useOrdersFindAll = (params: IFindAllOrdersParams) => {
	const { page = 1, limit = 50, orderId = "", customer = "", orderStatus } = params;

	const { data, error, isLoading } = useQuery({
		queryKey: ORDERS_QUERY_KEYS.FIND_ALL_ORDERS(page, limit, orderId, customer, orderStatus),
		queryFn: () => findAllOrdersRequest({ page, limit, orderId, customer, orderStatus }),
	});

	return {
		data,
		error,
		isLoading,
	};
};
