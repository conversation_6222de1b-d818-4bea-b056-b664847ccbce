{"name": "lmp-modular", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "host": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview --host", "prepare": "husky"}, "dependencies": {"@babel/types": "^7.28.1", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accessible-icon": "^1.1.7", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-router": "^1.129.8", "@tanstack/react-table": "^8.21.3", "@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.3", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "flatpickr": "^4.6.13", "framer-motion": "^11.16.0", "fuse.js": "^7.1.0", "jotai": "^2.11.3", "js-cookie": "^3.0.5", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-flatpickr": "^4.0.11", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-number-format": "^5.4.4", "recharts": "^3.1.0", "socket.io-client": "^4.8.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.5"}, "devDependencies": {"@eslint/js": "^9.31.0", "@tailwindcss/postcss": "^4.1.11", "@tanstack/eslint-plugin-query": "^5.62.9", "@tanstack/router-devtools": "^1.129.7", "@tanstack/router-plugin": "^1.129.8", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-flatpickr": "^3.8.11", "@vitejs/plugin-react-swc": "^3.11.0", "autoprefixer": "^10.4.20", "eslint": "^9.31.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "husky": "^9.1.7", "postcss": "^8.5.6", "tailwindcss": "^4.0.6", "tw-animate-css": "^1.3.5", "typescript": "~5.8.3", "typescript-eslint": "^8.38.0", "vite": "^7.0.5"}}