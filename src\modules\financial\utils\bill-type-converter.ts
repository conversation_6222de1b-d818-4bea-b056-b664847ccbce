import { BillTypeEnum } from "@/modules/person/enums/person-classification.enum";

/**
 * Converte o tipo de conta do backend (string) para o enum BillTypeEnum
 * @param backendType - Tipo vindo do backend ("A Pagar" ou "A Receber")
 * @returns BillTypeEnum correspondente
 */
export const convertBackendTypeToBillEnum = (backendType: string): BillTypeEnum => {
	return backendType === "A Pagar" ? BillTypeEnum.PAYABLE : BillTypeEnum.RECEIVABLE;
};

/**
 * Converte o enum BillTypeEnum para string de exibição
 * @param billType - Enum BillTypeEnum
 * @returns String para exibição ("A pagar" ou "A receber")
 */
export const convertBillEnumToDisplayString = (billType: BillTypeEnum): string => {
	return billType === BillTypeEnum.PAYABLE ? "A pagar" : "A receber";
};

/**
 * Converte o enum BillTypeEnum para string do backend
 * @param billType - Enum BillTypeEnum
 * @returns String do backend ("A Pagar" ou "A Receber")
 */
export const convertBillEnumToBackendString = (billType: BillTypeEnum): string => {
	return billType === BillTypeEnum.PAYABLE ? "A Pagar" : "A Receber";
};

/**
 * Verifica se o tipo é "A pagar"
 * @param type - Tipo da conta (string do backend ou enum)
 * @returns true se for "A pagar"
 */
export const isPayableType = (type: string | BillTypeEnum): boolean => {
	if (typeof type === "string") {
		return type === "A Pagar";
	}
	return type === BillTypeEnum.PAYABLE;
};

/**
 * Verifica se o tipo é "A receber"
 * @param type - Tipo da conta (string do backend ou enum)
 * @returns true se for "A receber"
 */
export const isReceivableType = (type: string | BillTypeEnum): boolean => {
	if (typeof type === "string") {
		return type === "A Receber";
	}
	return type === BillTypeEnum.RECEIVABLE;
};
