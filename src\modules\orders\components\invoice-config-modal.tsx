import { But<PERSON> } from "@/shared/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/shared/components/ui/dialog";
import { Label } from "@/shared/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";
import { FileText, User, MapPin } from "lucide-react";
import { useState } from "react";
import {
	InvoiceModelEnum,
	InvoiceFinalEnum,
	InvoiceBuyerPresenceEnum,
	InvoiceModelData,
	InvoiceFinalData,
	InvoiceBuyerPresenceData,
} from "../enums/invoice.enum";

export interface InvoiceConfig {
	invoiceModel: InvoiceModelEnum;
	finalCustomer: InvoiceFinalEnum;
	buyerPresence: InvoiceBuyerPresenceEnum;
}

interface InvoiceConfigModalProps {
	isOpen: boolean;
	onClose: () => void;
	onConfirm: (config: InvoiceConfig) => void;
	isLoading?: boolean;
}

export const InvoiceConfigModal = ({ isOpen, onClose, onConfirm, isLoading = false }: InvoiceConfigModalProps) => {
	const [invoiceModel, setInvoiceModel] = useState<InvoiceModelEnum>(InvoiceModelEnum.NFE);
	const [finalCustomer, setFinalCustomer] = useState<InvoiceFinalEnum>(InvoiceFinalEnum.FINALCUSTOMER);
	const [buyerPresence, setBuyerPresence] = useState<InvoiceBuyerPresenceEnum>(InvoiceBuyerPresenceEnum.InPerson);

	const handleConfirm = () => {
		onConfirm({
			invoiceModel,
			finalCustomer,
			buyerPresence,
		});
	};

	const handleClose = () => {
		if (!isLoading) {
			onClose();
		}
	};

	const selectedModelData = InvoiceModelData.find(item => item.value === invoiceModel);
	const selectedFinalData = InvoiceFinalData.find(item => item.value === finalCustomer);
	const selectedPresenceData = InvoiceBuyerPresenceData.find(item => item.value === buyerPresence);

	return (
		<Dialog open={isOpen} onOpenChange={handleClose}>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<FileText className="h-5 w-5 text-blue-600" />
						Configuração da Nota Fiscal
					</DialogTitle>
					<DialogDescription>
						Configure os parâmetros da nota fiscal antes de enviar para faturamento.
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-6 py-4">
					{/* Modelo da Nota Fiscal */}
					<div className="space-y-2">
						<Label className="text-sm font-medium flex items-center gap-2">
							<FileText className="h-4 w-4" />
							Modelo da Nota Fiscal
						</Label>
						<Select
							value={invoiceModel.toString()}
							onValueChange={(value) => setInvoiceModel(Number(value) as InvoiceModelEnum)}
							disabled={isLoading}
						>
							<SelectTrigger className="w-full">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								{InvoiceModelData.map((item) => (
									<SelectItem key={item.value} value={item.value.toString()}>
										<div className="flex flex-col">
											<span className="font-medium">{item.label}</span>
											<span className="text-xs text-gray-500">{item.description}</span>
										</div>
									</SelectItem>
								))}
							</SelectContent>
						</Select>
						{selectedModelData && (
							<p className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
								{selectedModelData.description}
							</p>
						)}
					</div>

					{/* Consumidor Final */}
					<div className="space-y-2">
						<Label className="text-sm font-medium flex items-center gap-2">
							<User className="h-4 w-4" />
							Consumidor Final
						</Label>
						<Select
							value={finalCustomer.toString()}
							onValueChange={(value) => setFinalCustomer(Number(value) as InvoiceFinalEnum)}
							disabled={isLoading}
						>
							<SelectTrigger className="w-full">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								{InvoiceFinalData.map((item) => (
									<SelectItem key={item.value} value={item.value.toString()}>
										<div className="flex flex-col">
											<span className="font-medium">{item.label}</span>
											<span className="text-xs text-gray-500">{item.description}</span>
										</div>
									</SelectItem>
								))}
							</SelectContent>
						</Select>
						{selectedFinalData && (
							<p className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
								{selectedFinalData.description}
							</p>
						)}
					</div>

					{/* Presença do Comprador */}
					<div className="space-y-2">
						<Label className="text-sm font-medium flex items-center gap-2">
							<MapPin className="h-4 w-4" />
							Presença do Comprador
						</Label>
						<Select
							value={buyerPresence.toString()}
							onValueChange={(value) => setBuyerPresence(Number(value) as InvoiceBuyerPresenceEnum)}
							disabled={isLoading}
						>
							<SelectTrigger className="w-full">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								{InvoiceBuyerPresenceData.map((item) => (
									<SelectItem key={item.value} value={item.value.toString()}>
										<div className="flex flex-col">
											<span className="font-medium">{item.label}</span>
											<span className="text-xs text-gray-500">{item.description}</span>
										</div>
									</SelectItem>
								))}
							</SelectContent>
						</Select>
						{selectedPresenceData && (
							<p className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
								{selectedPresenceData.description}
							</p>
						)}
					</div>
				</div>

				<DialogFooter>
					<Button
						variant="outline"
						onClick={handleClose}
						disabled={isLoading}
					>
						Cancelar
					</Button>
					<Button
						onClick={handleConfirm}
						disabled={isLoading}
						className="bg-blue-600 hover:bg-blue-700"
					>
						{isLoading ? "Enviando..." : "Confirmar e Enviar"}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};
