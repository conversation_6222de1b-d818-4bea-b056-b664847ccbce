name: Dependabot Build Check

on:
    pull_request:
        branches: [develop]
        types: [opened, synchronize, reopened]
        paths-ignore:
            - "**.md"

jobs:
    build:
        if: github.actor == 'dependabot[bot]'
        runs-on: ubuntu-latest

        steps:
            - name: Checkout repo
              uses: actions/checkout@v3

            - name: Setup Node
              uses: actions/setup-node@v4
              with:
                  node-version: 22

            - name: Install dependencies
              run: npm ci

            - name: Build project
              run: npm run build
