import { ChevronLeft, ChevronRight } from "lucide-react";
import * as React from "react";
import { DayPicker } from "react-day-picker";

import { buttonVariants } from "@/shared/components/ui/button";
import { cn } from "@/shared/lib/utils.ts";

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

function Calendar({ className, classNames, showOutsideDays = true, ...props }: CalendarProps) {
	return (
		<DayPicker
			showOutsideDays={showOutsideDays}
			className={cn("p-3 bg-white text-[#333]", className)}
			classNames={{
				months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
				month: "space-y-4",
				caption: "flex justify-center pt-1 relative items-center",
				caption_label: "text-sm font-medium text-[#333]",
				nav: "space-x-1 flex items-center",
				nav_button: cn(
					buttonVariants({ variant: "outline" }),
					"h-7 w-7 bg-transparent p-0 opacity-70 hover:opacity-100 border-mainColor text-mainColor"
				),
				nav_button_previous: "absolute left-1",
				nav_button_next: "absolute right-1",
				table: "w-full border-collapse space-y-1",
				head_row: "flex",
				head_cell: "rounded-md w-9 font-normal text-[0.8rem] text-[#666]",
				row: "flex w-full mt-2",
				cell: cn(
					"h-9 w-9 text-center text-sm p-0 relative",
					"[&:has([aria-selected].day-range-end)]:rounded-r-md",
					"[&:has([aria-selected].day-outside)]:bg-mainColor/10",
					"[&:has([aria-selected])]:bg-mainColor/10",
					"first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md"
				),
				day: cn(buttonVariants({ variant: "ghost" }), "h-9 w-9 p-0 font-normal aria-selected:opacity-100"),
				day_range_end: "day-range-end",
				day_selected: "bg-mainColor text-white hover:bg-mainColor/90 focus:bg-mainColor/90",
				day_today: "bg-mainColor/10 text-[#333]",
				day_outside: "day-outside text-[#999] aria-selected:bg-mainColor/10 aria-selected:text-[#999]",
				day_disabled: "text-[#999] opacity-50",
				day_range_middle: "aria-selected:bg-mainColor/10 aria-selected:text-[#333]",
				day_hidden: "invisible",
				...classNames,
			}}
			components={{
				Chevron: ({ orientation }) => 
					orientation === "left" ? 
						<ChevronLeft className="h-4 w-4" /> : 
						<ChevronRight className="h-4 w-4" />
			}}
			{...props}
		/>
	);
}
Calendar.displayName = "Calendar";

export { Calendar };
