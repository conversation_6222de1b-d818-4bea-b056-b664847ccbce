export const formatCurrency = (value: number | string | undefined): string => {
	console.log("formatCurrency", value);
	if (!value) return "R$ 0,00";
	const number = typeof value === "string" ? parseFloat(value) : value;

	if (isNaN(number)) return "R$ 0,00";

	return new Intl.NumberFormat("pt-BR", {
		style: "currency",
		currency: "BRL",
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	}).format(number);
};
