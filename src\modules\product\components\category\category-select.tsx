import { Badge } from "@/shared/components/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@/shared/components/ui/popover";
import { cn } from "@/shared/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import { AlertCircle, Check, ChevronsUpDown, Search, Tag, X } from "lucide-react";
import React, { JSX, useEffect, useMemo, useState } from "react";
import { ICategoryFindDto } from "../../dtos/category/find";
import { useCategoryFindAll } from "../../hooks/category/find-all.hook";

type CategorySelectProps = Readonly<{
	value: string | null;
	onChange: (value: string | null) => void;
	error?: string;
	label?: string;
	required?: boolean;
	icon?: React.ReactNode;
	placeholder?: string;
	groupId?: string | null;
	disabled?: boolean;
}>;

export const CategorySelect: React.FC<CategorySelectProps> = ({
	value = null,
	onChange,
	error,
	label = "Categoria",
	required = false,
	icon = <Tag size={16} />,
	placeholder = "Selecione ou pesquise uma categoria...",
	groupId = null,
	disabled = false,
}): JSX.Element => {
	const [open, setOpen] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const {
		data: categoryData,
		isLoading,
		isError: isLoadingError,
	} = useCategoryFindAll({
		groupId: groupId ?? undefined,
	});

	const allCategories: ICategoryFindDto[] = categoryData?.success ? categoryData.data.data : [];

	useEffect(() => {
		if (value && groupId) {
			const selectedCategoryExistsInGroup = allCategories.some(cat => cat.id.toString() === value);
			if (!selectedCategoryExistsInGroup && !isLoading) {
				onChange(null);
			}
		}
	}, [groupId, allCategories, isLoading]);

	const filteredCategories = useMemo(() => {
		if (!searchTerm) {
			return allCategories;
		}
		return allCategories.filter(category => category.name.toLowerCase().includes(searchTerm.toLowerCase()));
	}, [allCategories, searchTerm]);

	const handleSelectCategory = (categoryId: number | null): void => {
		if (categoryId === null) {
			onChange(null);
		} else {
			const stringId = categoryId.toString();
			onChange(stringId);
		}
	};

	const selectedCategoryDetail = useMemo(() => {
		if (!value) return null;
		return allCategories.find(cat => cat.id.toString() === value) || null;
	}, [value, allCategories]);

	if (isLoadingError) {
		return (
			<div className="relative">
				<label className="flex items-center gap-1.5 text-sm font-semibold text-gray-700 mb-1.5">
					{icon && <span className="text-gray-500">{icon}</span>}
					{label} {required && <span className="text-red-500 text-xs">*</span>}
				</label>
				<div className="border border-red-200 rounded-lg w-full px-4 py-2 min-h-[42px] bg-red-50/30">
					<div className="flex items-center gap-2 text-red-500">
						<AlertCircle size={16} />
						<span className="text-sm">Erro ao carregar categorias</span>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="relative">
			<label className="flex items-center gap-1.5 text-sm font-semibold text-gray-700 mb-1.5">
				{icon && <span className="text-gray-500">{icon}</span>}
				{label} {required && <span className="text-red-500 text-xs">*</span>}
			</label>

			<Popover open={disabled ? false : open} onOpenChange={disabled ? undefined : setOpen}>
				<PopoverTrigger asChild>
					<motion.button
						type="button"
						whileHover={disabled ? {} : { scale: 1.01 }}
						whileTap={disabled ? {} : { scale: 0.99 }}
						transition={{ type: "spring", stiffness: 400, damping: 17 }}
						className={cn(
							"w-full justify-between min-h-[42px] h-auto py-2 px-3 text-left font-normal",
							"border rounded-lg",
							"focus:outline-none focus:ring-2 focus:ring-mainColor/50",
							"flex items-center",
							disabled ? "bg-gray-100 cursor-not-allowed text-gray-500 border-gray-200" : "bg-white/50 hover:border-gray-300",
							error ? "border-red-300" : "border-gray-200",
							!value && "text-muted-foreground"
						)}
						disabled={disabled || (isLoading && !allCategories.length)}
					>
						<div className="flex items-center flex-1">
							{isLoading && !allCategories.length ? (
								<span className="text-sm text-gray-500">Carregando categorias...</span>
							) : selectedCategoryDetail ? (
								<Badge
									variant="secondary"
									className="py-0.5 px-2 rounded-sm bg-mainColor/10 text-mainColor hover:bg-mainColor/20 cursor-default"
								>
									{selectedCategoryDetail.name}
									{!disabled && (
										<X
											size={12}
											className="ml-1 cursor-pointer"
											onClick={e => {
												e.stopPropagation();
												handleSelectCategory(null);
												setOpen(false);
											}}
										/>
									)}
								</Badge>
							) : (
								<span className="text-sm text-gray-500">{placeholder}</span>
							)}
						</div>
						<motion.div animate={{ rotate: open ? 180 : 0 }} transition={{ duration: 0.2 }}>
							<ChevronsUpDown size={16} className="text-gray-500 ml-2" />
						</motion.div>
					</motion.button>
				</PopoverTrigger>
				<PopoverContent className="w-[--radix-popover-trigger-width] z-[10000] p-0 max-h-[300px] overflow-auto" align="start">
					<div className="flex flex-col h-full max-h-[300px]">
						<div className="sticky top-0 z-10 bg-white">
							<span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
								<Search size={16} />
							</span>
							<input
								placeholder="Buscar categoria..."
								value={searchTerm}
								onChange={e => setSearchTerm(e.target.value)}
								className="h-10 pl-10 pr-3 text-sm border-b border-gray-200 focus:outline-none focus:ring-0 w-full"
							/>
						</div>
						<div className="overflow-y-auto">
							{isLoading && filteredCategories.length === 0 ? (
								<div className="p-2 text-center text-gray-500 text-sm">Carregando...</div>
							) : (
								<ul className="py-1">
									<AnimatePresence>
										{filteredCategories.map(category => {
											const isSelected = value === category.id.toString();
											return (
												<motion.li
													key={category.id + category.name}
													initial={{ opacity: 0, y: 10 }}
													animate={{ opacity: 1, y: 0 }}
													exit={{ opacity: 0, y: -10 }}
													transition={{ duration: 0.2 }}
													className="flex items-center justify-between py-2.5 px-3 cursor-pointer hover:bg-mainColor/5"
													onClick={() => {
														handleSelectCategory(category.id);
														setOpen(false); // Fechar o popover após a seleção
													}}
												>
													<div className="flex items-center">
														<Tag size={14} className="mr-2 text-gray-500" />
														<span className="font-medium text-sm">{category.name}</span>
													</div>
													{isSelected && <Check size={16} className="text-mainColor" />}
												</motion.li>
											);
										})}
									</AnimatePresence>
									{!isLoading && filteredCategories.length === 0 && (
										<div className="p-2 text-center text-gray-500 text-sm">
											{searchTerm ? "Nenhuma categoria encontrada" : "Nenhuma categoria disponível"}
										</div>
									)}
								</ul>
							)}
						</div>
					</div>
				</PopoverContent>
			</Popover>

			{error && (
				<motion.div
					initial={{ opacity: 0, y: -10 }}
					animate={{ opacity: 1, y: 0 }}
					className="absolute left-0 mt-1 text-xs text-red-500 flex items-center gap-1"
				>
					<AlertCircle size={12} />
					<span className="font-medium">{error}</span>
				</motion.div>
			)}
		</div>
	);
};
