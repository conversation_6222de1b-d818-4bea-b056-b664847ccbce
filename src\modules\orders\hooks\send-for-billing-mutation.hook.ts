import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { sendForBillingRequest } from "../api/requests/send-for-billing";
import { ORDERS_MUTATION_KEYS } from "../data/query-keys";

export interface IUseSendForBillingMutationProps {
	onSuccess?: () => void;
}

export const useSendForBillingMutation = ({ onSuccess }: IUseSendForBillingMutationProps = {}) => {
	const sendForBillingMutation = useMutation({
		mutationKey: ORDERS_MUTATION_KEYS.SEND_FOR_BILLING,
		mutationFn: async (orderId: number) => {
			const response = await sendForBillingRequest(orderId);
			if (response.success) {
				return response.data;
			}
			throw new Error(response.data.message);
		},
		onMutate: () => {
			const pendingToastId = toast.loading("Enviando pedido para faturamento...");
			return { pendingToastId };
		},
		onSuccess: (data, _, context) => {
			if (context?.pendingToastId) {
				toast.success(data.message, { id: context.pendingToastId });
			}
			onSuccess?.();
		},
		onError: (error: Error, _, context) => {
			if (context?.pendingToastId) {
				toast.error(error.message, { id: context.pendingToastId });
			}
		},
	});

	return {
		sendForBilling: sendForBillingMutation.mutateAsync,
		isLoading: sendForBillingMutation.isPending,
		sendForBillingMutation,
	};
};
