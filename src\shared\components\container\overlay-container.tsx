import { IOverlayContainerProps } from "@/shared/types/containers/overlay-container.type";
import { AnimatePresence, motion } from "framer-motion";
import React, { useEffect, useRef } from "react";
import ReactDOM from "react-dom";

const overlayVariants = {
	hidden: { opacity: 0 },
	visible: { opacity: 1 },
	exit: { opacity: 0 },
};

export const OverlayContainer = ({ isVisible, onClose, children }: IOverlayContainerProps) => {
	const clickStartRef = useRef<EventTarget | null>(null);

	const handleMouseDown = (event: React.MouseEvent<HTMLDivElement>) => {
		clickStartRef.current = event.target;
	};

	const handleMouseUp = (event: React.MouseEvent<HTMLDivElement>) => {
		if (clickStartRef.current === event.currentTarget && event.target === event.currentTarget) {
			onClose();
		}
		clickStartRef.current = null;
	};

	useEffect(() => {
		if (!isVisible) {
			document.body.style.overflow = "";
			return;
		}

		document.body.style.overflow = "hidden";

		const handleKeyDown = (event: KeyboardEvent) => {
			if (event.key === "Escape") {
				onClose();
			}
		};

		document.addEventListener("keydown", handleKeyDown);
		return () => {
			document.removeEventListener("keydown", handleKeyDown);
			document.body.style.overflow = "";
		};
	}, [isVisible, onClose]);

	const overlay = (
		<AnimatePresence>
			{isVisible && (
				<motion.div
					className="fixed inset-0 z-20 bg-black/40 backdrop-blur-sm flex justify-center items-center"
					variants={overlayVariants}
					initial="hidden"
					animate="visible"
					exit="exit"
					style={{
						zIndex: 9999,
						width: "100vw",
						height: "100svh",
						minHeight: "100vh",
						top: 0,
						left: 0,
					}}
					onMouseDown={handleMouseDown}
					onMouseUp={handleMouseUp}
				>
					{children}
				</motion.div>
			)}
		</AnimatePresence>
	);

	return typeof window !== "undefined" ? ReactDOM.createPortal(overlay, document.body) : null;
};
