import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { Button } from "@/shared/components/ui/button";
import { Download, X, FileText } from "lucide-react";
import { motion } from "framer-motion";
import { IDashboardReport } from "../../types/dashboard.types";
import { useResponsiveCharts } from "../../hooks/use-responsive-charts";
import React from "react";

interface IReportModalProps {
	reportId: number;
	reports: IDashboardReport[];
	onClose: () => void;
	onGeneratePDF: (reportId: number) => void;
}

export const ReportModal = React.memo(({ reportId, reports, onClose, onGeneratePDF }: IReportModalProps) => {
	const { isMobile, isTablet } = useResponsiveCharts();
	const report = reports.find(r => r.id === reportId);

	if (!report) return null;

	const handleGeneratePDF = () => {
		onGeneratePDF(reportId);
	};

	return (
		<OverlayContainer isVisible={true} onClose={onClose}>
			<motion.div
				initial={{ opacity: 0, scale: 0.95, y: isMobile ? 50 : 0 }}
				animate={{ opacity: 1, scale: 1, y: 0 }}
				exit={{ opacity: 0, scale: 0.95, y: isMobile ? 50 : 0 }}
				transition={{ duration: 0.2 }}
				className={`
					bg-white rounded-[20px] w-full overflow-hidden shadow-2xl
					${isMobile ? "max-w-[95vw] max-h-[95vh] mx-2" : isTablet ? "max-w-3xl max-h-[90vh]" : "max-w-4xl max-h-[90vh]"}
				`}
				onClick={e => e.stopPropagation()}
			>
				{/* Header */}
				<div
					className={`
					flex items-center justify-between border-b border-gray-200
					${isMobile ? "p-4 flex-col gap-4" : "p-6 flex-row"}
				`}
				>
					<div className={`flex items-center gap-3 ${isMobile ? "w-full" : ""}`}>
						<div className={`${isMobile ? "p-1.5" : "p-2"} bg-blue-100 rounded-lg`}>
							<FileText size={isMobile ? 18 : 20} className="text-blue-600" />
						</div>
						<div className="flex-1 min-w-0">
							<h2
								className={`
								font-semibold text-gray-900 truncate
								${isMobile ? "text-base" : "text-lg"}
							`}
							>
								{report.nome}
							</h2>
							<p
								className={`
								text-gray-500 truncate
								${isMobile ? "text-xs" : "text-sm"}
							`}
							>
								{report.descricao}
							</p>
						</div>
					</div>

					<div className={`flex items-center gap-2 ${isMobile ? "w-full" : ""}`}>
						{isMobile ? (
							<>
								<Button
									onClick={handleGeneratePDF}
									className="flex-1 flex items-center justify-center gap-2 bg-mainColor hover:bg-mainColor/90 text-white text-sm"
								>
									<Download size={14} />
									Exportar PDF
								</Button>
								<Button variant="outline" onClick={onClose} className="p-2.5">
									<X size={16} />
								</Button>
							</>
						) : (
							<>
								<Button onClick={handleGeneratePDF} className="flex items-center gap-2 bg-mainColor hover:bg-mainColor/90 text-white">
									<Download size={16} />
									Exportar PDF
								</Button>
								<Button variant="outline" onClick={onClose} className="p-2">
									<X size={16} />
								</Button>
							</>
						)}
					</div>
				</div>

				{/* Content */}
				<div
					className={`
					overflow-y-auto
					${isMobile ? "p-4 max-h-[calc(95vh-140px)]" : "p-6 max-h-[calc(90vh-120px)]"}
				`}
				>
					<div className={`space-y-${isMobile ? "4" : "6"}`}>
						{/* Report Description */}
						<div className={`bg-gray-50 rounded-lg ${isMobile ? "p-3" : "p-4"}`}>
							<p className={`text-gray-600 ${isMobile ? "text-xs" : "text-sm"}`}>
								Conteúdo detalhado do relatório "{report.nome}". Aqui seriam exibidos os dados completos do relatório com tabelas,
								gráficos e análises detalhadas baseadas nos filtros selecionados.
							</p>
						</div>

						{/* Sample Chart Area */}
						<div className={`border border-gray-200 rounded-lg bg-gray-50 ${isMobile ? "p-4" : "p-6"}`}>
							<div className={`flex items-center justify-center text-gray-500 ${isMobile ? "h-48" : "h-64"}`}>
								<div className="text-center">
									<FileText size={isMobile ? 36 : 48} className="mx-auto mb-4 text-gray-400" />
									<p className={`font-medium ${isMobile ? "text-base" : "text-lg"}`}>Visualização do Relatório</p>
									<p className={`${isMobile ? "text-xs" : "text-sm"} mt-2`}>
										Aqui seria exibido o conteúdo específico do relatório "{report.nome}"
									</p>
								</div>
							</div>
						</div>

						{/* Sample Data */}
						<div className="border border-gray-200 rounded-lg overflow-hidden">
							<div className={`bg-gray-50 border-b border-gray-200 ${isMobile ? "px-4 py-2" : "px-6 py-3"}`}>
								<h3 className={`font-medium text-gray-900 ${isMobile ? "text-sm" : "text-base"}`}>Dados do Relatório</h3>
							</div>

							{isMobile ? (
								/* Mobile List Layout */
								<div className="bg-white">
									{/* Mobile Summary Cards */}
									<div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100">
										<div className="grid grid-cols-3 gap-3">
											<div className="text-center">
												<div className="text-lg font-bold text-blue-600">5</div>
												<div className="text-xs text-gray-600">Total Itens</div>
											</div>
											<div className="text-center">
												<div className="text-lg font-bold text-green-600">3</div>
												<div className="text-xs text-gray-600">Normais</div>
											</div>
											<div className="text-center">
												<div className="text-lg font-bold text-red-600">1</div>
												<div className="text-xs text-gray-600">Críticos</div>
											</div>
										</div>
									</div>

									{/* Mobile Items List */}
									{[1, 2, 3, 4, 5].map(item => {
										const quantidade = Math.floor(Math.random() * 100);
										const valor = (Math.random() * 1000).toFixed(2);
										const status = item % 3 === 0 ? "Crítico" : item % 2 === 0 ? "Normal" : "Atenção";
										const statusColor =
											item % 3 === 0
												? "bg-red-100 text-red-800 border-red-200"
												: item % 2 === 0
													? "bg-green-100 text-green-800 border-green-200"
													: "bg-yellow-100 text-yellow-800 border-yellow-200";

										return (
											<div
												key={item}
												className="p-4 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 transition-colors"
											>
												<div className="flex items-start justify-between mb-3">
													<div className="flex-1">
														<h4 className="text-sm font-semibold text-gray-900 mb-1">Item {item}</h4>
														<div className="flex items-center">
															<span
																className={`
																px-2 py-1 text-xs font-medium rounded-full border
																${statusColor}
															`}
															>
																{status}
															</span>
														</div>
													</div>
												</div>

												<div className="grid grid-cols-2 gap-3">
													<div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
														<div className="text-xs text-gray-500 font-medium mb-1">Quantidade</div>
														<div className="text-sm font-semibold text-gray-900">{quantidade} unidades</div>
													</div>
													<div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
														<div className="text-xs text-gray-500 font-medium mb-1">Valor Total</div>
														<div className="text-sm font-semibold text-gray-900">R$ {valor}</div>
													</div>
												</div>
											</div>
										);
									})}
								</div>
							) : (
								/* Desktop Table Layout */
								<div className="overflow-x-auto">
									<table className="min-w-full divide-y divide-gray-200">
										<thead className="bg-gray-50">
											<tr>
												<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
													Item
												</th>
												<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
													Quantidade
												</th>
												<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
													Valor
												</th>
												<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
													Status
												</th>
											</tr>
										</thead>
										<tbody className="bg-white divide-y divide-gray-200">
											{[1, 2, 3, 4, 5].map(item => (
												<tr key={item} className="hover:bg-gray-50">
													<td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Item {item}</td>
													<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
														{Math.floor(Math.random() * 100)}
													</td>
													<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
														R$ {(Math.random() * 1000).toFixed(2)}
													</td>
													<td className="px-6 py-4 whitespace-nowrap">
														<span
															className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
																item % 3 === 0
																	? "bg-red-100 text-red-800"
																	: item % 2 === 0
																		? "bg-green-100 text-green-800"
																		: "bg-yellow-100 text-yellow-800"
															}`}
														>
															{item % 3 === 0 ? "Crítico" : item % 2 === 0 ? "Normal" : "Atenção"}
														</span>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							)}
						</div>
					</div>
				</div>
			</motion.div>
		</OverlayContainer>
	);
});

ReportModal.displayName = "ReportModal";
