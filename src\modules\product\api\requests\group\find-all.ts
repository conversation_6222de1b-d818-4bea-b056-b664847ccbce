import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IGroupListProps, IGroupListResponse } from "../../../dtos/group/find-all-groups.dto";
import { PRODUCT_GROUP_ROUTES } from "../../endpoints";

export const productGroupFindAllRequest = async ({ page, limit, search }: IGroupListProps): Promise<ApiResponse<IGroupListResponse>> => {
	return createRequest({
		path: PRODUCT_GROUP_ROUTES.FIND_ALL({ page, limit, search: search || "" }),
		method: "GET",
	});
};
