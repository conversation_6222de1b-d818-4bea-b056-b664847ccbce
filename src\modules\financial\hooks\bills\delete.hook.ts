import { deleteBillRequest } from "@/modules/financial/api/requests/bills/delete";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { FINANCIAL_MUTATIONS_KEYS, FINANCIAL_QUERY_KEYS } from "../../data/query.keys";

export const useDeleteBill = () => {
	const queryClient = useQueryClient();

	const mutation = useMutation({
		mutationKey: FINANCIAL_MUTATIONS_KEYS.DELETE_BILL,
		mutationFn: async ({ id }: { id: number }) => {
			const res = await deleteBillRequest({ id });
			if (!res.success) throw new Error(res.data.message);
			return res;
		},
		onSuccess: ({ data }) => {
			toast.dismiss();
			toast.success(data.message);
			queryClient.invalidateQueries({ queryKey: [FINANCIAL_QUERY_KEYS.FIND_ALL_BILLS] });
		},
		onError: error => {
			toast.error(error?.message || "Erro ao excluir conta.");
		},
	});

	return {
		onDelete: (params: { id: number }) => mutation.mutate(params),
		...mutation,
	};
};
