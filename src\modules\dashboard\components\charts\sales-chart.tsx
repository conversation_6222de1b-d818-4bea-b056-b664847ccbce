import { <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2 } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, ResponsiveContainer, <PERSON>ltip, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON> } from "recharts";
import { IChartData } from "../../types/dashboard.types";
import { useResponsiveCharts } from "../../hooks/use-responsive-charts";
import React from "react";

interface ISalesChartProps {
	data: IChartData[];
	isLoading?: boolean;
}

const CustomTooltip = ({ active, payload, label }: any) => {
	if (active && payload && payload.length) {
		const value = payload[0].value;
		const formattedValue = value?.toLocaleString("pt-BR", {
			style: "currency",
			currency: "BRL",
		});

		return (
			<div className="bg-white/95 backdrop-blur-sm p-4 border border-gray-200/50 rounded-xl shadow-xl">
				<p className="text-sm font-semibold text-gray-900 mb-1">{label}</p>
				<div className="flex items-center gap-2">
					<div className="w-3 h-3 rounded-full bg-gradient-to-r from-blue-500 to-blue-600"></div>
					<p className="text-sm font-medium text-blue-600">Vendas: {formattedValue}</p>
				</div>
			</div>
		);
	}
	return null;
};

const EmptyState = () => (
	<div className="flex flex-col items-center justify-center h-full text-gray-500">
		<BarChart2 size={48} className="mb-3 opacity-50" />
		<p className="text-sm font-medium">Nenhum dado disponível</p>
		<p className="text-xs text-gray-400">Dados de vendas aparecerão aqui</p>
	</div>
);

const LoadingState = () => (
	<div className="flex flex-col items-center justify-center h-full text-gray-500">
		<Loader2 size={32} className="animate-spin mb-3 text-blue-500" />
		<p className="text-sm font-medium">Carregando dados...</p>
	</div>
);

export const SalesChart = React.memo(({ data, isLoading = false }: ISalesChartProps) => {
	const { isMobile, chartHeight, margins, fontSize, barMaxSize } = useResponsiveCharts();
	const hasData = data && data.length > 0;

	return (
		<div className="bg-gradient-to-br from-white to-gray-50/50 p-4 sm:p-6 rounded-2xl shadow-sm border border-gray-100/50 hover:shadow-md transition-all duration-300">
			<div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
				<div className="flex items-center gap-3">
					<div className="p-2.5 bg-gradient-to-br from-blue-100 to-blue-50 rounded-xl shadow-sm">
						<BarChart2 size={20} className="text-blue-600" />
					</div>
					<div>
						<h3 className="font-semibold text-gray-900 text-base sm:text-lg">Vendas Mensais</h3>
						<p className="text-xs sm:text-sm text-gray-500">Evolução das vendas ao longo do tempo</p>
					</div>
				</div>
				<button className="flex items-center gap-2 text-xs sm:text-sm text-blue-600 hover:text-blue-700 transition-colors self-start sm:self-auto px-3 py-1.5 rounded-lg hover:bg-blue-50">
					<TrendingUp size={14} />
					<span className="hidden sm:inline">Ver detalhes</span>
					<span className="sm:hidden">Detalhes</span>
				</button>
			</div>

			<div className={`w-full transition-all duration-300`} style={{ height: chartHeight }}>
				{isLoading ? (
					<LoadingState />
				) : !hasData ? (
					<EmptyState />
				) : (
					<ResponsiveContainer width="100%" height="100%">
						<BarChart data={data} margin={margins}>
							<defs>
								<linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
									<stop offset="0%" stopColor="#3b82f6" stopOpacity={0.9} />
									<stop offset="100%" stopColor="#1d4ed8" stopOpacity={0.7} />
								</linearGradient>
							</defs>
							<CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" strokeOpacity={0.6} vertical={false} />
							<XAxis
								dataKey="name"
								axisLine={false}
								tickLine={false}
								tick={{
									fontSize: fontSize.tick,
									fill: "#6b7280",
									fontWeight: 500,
								}}
								interval={isMobile ? "preserveStartEnd" : 0}
							/>
							<YAxis
								axisLine={false}
								tickLine={false}
								tick={{
									fontSize: fontSize.tick,
									fill: "#6b7280",
									fontWeight: 500,
								}}
								tickFormatter={value => {
									if (isMobile) {
										return `R$ ${(value / 1000).toFixed(0)}k`;
									}
									return `R$ ${value.toLocaleString("pt-BR")}`;
								}}
								width={isMobile ? 50 : 80}
							/>
							<Tooltip content={<CustomTooltip />} />
							<Bar
								dataKey="valor"
								fill="url(#barGradient)"
								radius={[6, 6, 0, 0]}
								className="hover:opacity-80 transition-all duration-200 drop-shadow-sm"
								maxBarSize={barMaxSize}
							/>
						</BarChart>
					</ResponsiveContainer>
				)}
			</div>
		</div>
	);
});

SalesChart.displayName = "SalesChart";
