import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/ui/table";
import { formatDate } from "@/shared/lib/format-date";

import { Calendar, Package, TrendingDown, TrendingUp } from "lucide-react";
import { IMovementItem } from "../../dtos/find-all-movements.dto";

interface MovementsTableProps {
	movements: IMovementItem[];
}

export const MovementsTable: React.FC<MovementsTableProps> = ({ movements }) => {
	return (
		<div className="border rounded-[15px] border-gray-200">
			<Table className="w-full rounded-[15px] overflow-hidden text-sm min-w-[700px]">
				<TableHeader className="bg-gradient-to-r from-gray-50 to-gray-100">
					<TableRow>
						<TableHead className="py-4 px-6 text-gray-600 font-semibold">
							<div className="flex items-center gap-2">
								<Package size={16} />
								Produto
							</div>
						</TableHead>
						<TableHead className="py-4 px-6 text-gray-600 font-semibold">
							<div className="flex items-center gap-2">
								{/* Ícone para Descrição pode ser adicionado aqui, ex: MessageSquare */}
								Descrição
							</div>
						</TableHead>
						<TableHead className="py-4 px-6 text-gray-600 font-semibold">
							<div className="flex items-center gap-2">
								<TrendingUp size={16} />
								Movimentação
							</div>
						</TableHead>
						<TableHead className="py-4 px-6 text-gray-600 font-semibold">
							<div className="flex items-center gap-2">
								<Calendar size={16} />
								Data
							</div>
						</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{movements.map(movement => {
						const isPositive = movement.quantity > 0;
						const isNegative = movement.quantity < 0;

						return (
							<TableRow key={movement.id} className="border-b border-gray-100 hover:bg-gray-50/80 transition-colors">
								<TableCell className="py-4 px-6">
									<div className="flex items-center gap-2">
										<span className="font-medium text-gray-900">{movement.product}</span>
									</div>
								</TableCell>
								<TableCell className="py-4 px-6">
									<span className="text-gray-700">{movement.description}</span>
								</TableCell>
								<TableCell className="py-4 px-6">
									<div className="flex items-center gap-2">
										{isPositive && (
											<div className="bg-green-100 p-1.5 rounded-lg">
												<TrendingUp size={14} className="text-green-600" />
											</div>
										)}
										{isNegative && (
											<div className="bg-red-100 p-1.5 rounded-lg">
												<TrendingDown size={14} className="text-red-600" />
											</div>
										)}
										<div className="flex flex-col">
											<span
												className={`font-semibold ${
													isPositive ? "text-green-600" : isNegative ? "text-red-600" : "text-gray-600"
												}`}
											>
												{isPositive ? "+" : ""}
												{movement.quantity}
											</span>
											<span className="text-xs text-gray-500">{isPositive ? "Entrada" : isNegative ? "Saída" : "Neutro"}</span>
										</div>
									</div>
								</TableCell>
								<TableCell className="py-4 px-6">
									<div className="flex items-center gap-2">
										<div className="bg-gray-100 p-1.5 rounded-lg">
											<Calendar size={14} className="text-gray-600" />
										</div>
										<span className="text-gray-700">{formatDate(movement.createdAt)}</span>
									</div>
								</TableCell>
							</TableRow>
						);
					})}
				</TableBody>
			</Table>
		</div>
	);
};
