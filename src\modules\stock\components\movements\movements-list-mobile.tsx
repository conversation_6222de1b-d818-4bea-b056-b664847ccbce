import { formatDate } from "@/shared/lib/format-date";
import { Calendar, Hash as HashIcon, Package, TrendingDown, TrendingUp } from "lucide-react";
import { IMovementItem } from "../../dtos/find-all-movements.dto";

interface MovementsListMobileProps {
	movements: IMovementItem[];
}

export const MovementsListMobile: React.FC<MovementsListMobileProps> = ({ movements }) => {
	return (
		<div className="md:hidden space-y-4">
			{movements.map(movement => {
				const isPositive = movement.quantity > 0;
				const isNegative = movement.quantity < 0;

				return (
					<div key={movement.id} className="bg-white rounded-[15px] border border-gray-200 p-4 shadow-sm hover:shadow-md transition-shadow">
						{/* Header com produto e ID */}
						<div className="flex justify-between items-start mb-4">
							<div className="flex items-center gap-3 flex-1">
								<div className="bg-blue-100 p-2 rounded-lg">
									<Package size={18} className="text-blue-600" />
								</div>
								<div>
									<h3 className="font-semibold text-gray-900 text-base">{movement.product}</h3>
									<div className="flex items-center gap-1 mt-1">
										<HashIcon size={12} className="text-gray-400" />
										<span className="text-xs text-gray-500 font-mono">{movement.id}</span>
									</div>
								</div>
							</div>

							{/* Badge de movimentação */}
							<div
								className={`px-3 py-1 rounded-full text-xs font-medium ${
									isPositive ? "bg-green-100 text-green-700" : isNegative ? "bg-red-100 text-red-700" : "bg-gray-100 text-gray-700"
								}`}
							>
								{isPositive ? "Entrada" : isNegative ? "Saída" : "Neutro"}
							</div>
						</div>

						{/* Quantidade com ícone */}
						<div className="flex items-center justify-center mb-4 p-3 bg-gray-50 rounded-lg">
							<div className="flex items-center gap-3">
								{isPositive && (
									<div className="bg-green-100 p-2 rounded-lg">
										<TrendingUp size={20} className="text-green-600" />
									</div>
								)}
								{isNegative && (
									<div className="bg-red-100 p-2 rounded-lg">
										<TrendingDown size={20} className="text-red-600" />
									</div>
								)}
								<div className="text-center">
									<p className="text-xs text-gray-500 mb-1">Quantidade</p>
									<p
										className={`text-xl font-bold ${
											isPositive ? "text-green-600" : isNegative ? "text-red-600" : "text-gray-600"
										}`}
									>
										{isPositive ? "+" : ""}
										{movement.quantity}
									</p>
								</div>
							</div>
						</div>

						{/* Data */}
						<div className="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 p-2 rounded-lg">
							<div className="bg-gray-200 p-1.5 rounded-md">
								<Calendar size={14} className="text-gray-600" />
							</div>
							<span className="font-medium">{formatDate(movement.createdAt)}</span>
						</div>

						{/* Descrição */}
						{movement.description && (
							<div className="mt-3 flex items-start gap-2 text-sm text-gray-600 bg-gray-50 p-2 rounded-lg">
								{/* Ícone para Descrição pode ser adicionado aqui, ex: MessageSquare */}
								{/* <div className="bg-gray-200 p-1.5 rounded-md">
									<MessageSquare size={14} className="text-gray-600" />
								</div> */}
								<p className="text-sm text-gray-700">{movement.description}</p>
							</div>
						)}
					</div>
				);
			})}
		</div>
	);
};
