import { buildQueryParams } from "@/shared/lib/build-query-params";
import { IFindAllParams } from "../requests/find-all";

export const PERSON_ENDPOINTS = {
	CREATE: "/person/create",
   FIND_ALL_CUSTOMERS: ({ page, limit, name, document }: { page: number; limit: number; name?: string; document?: string }) => {
	   const base = `/person/find-all-customers/${page}/${limit}`;
	   return buildQueryParams(base, { name, document });
   },
   find_ALL_SUPPLIERS: ({ page, limit, filter }: { page: number; limit: number; filter?: string }) => {
	   const base = `/person/find-all-suppliers/${page}/${limit}`;
	   return buildQueryParams(base, { filter });
   },
	FIND_ALL: ({ page, limit, search, classification }: IFindAllParams) =>
		buildQueryParams(`/person/find-all/${page}/${limit}`, {
			search,
			classification,
		}),
} as const;
