import { PaymentMethodData, PaymentMethodEnum } from "@/modules/sales-panel/data/enums/payment.enum";
import { useAddPayment } from "@/modules/sales-panel/hooks/payment/add-payment.hook";
import { orderIdAtom } from "@/modules/sales-panel/states/order-id.state";
import { orderInfoAtom } from "@/modules/sales-panel/states/order-info.state";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import { useAtomValue } from "jotai";
import { Plus } from "lucide-react";
import React, { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { NumericFormat } from "react-number-format";
import * as z from "zod";

const PaymentFormSchema = z
	.object({
		paymentMethod: z.nativeEnum(PaymentMethodEnum, {
			message: "Selecione a forma de pagamento",
		}),
		paymentValue: z
			.string()
			.optional()
			.transform((value, context) => {
				if (!value || value.trim() === "") {
					return "TOTAL_ORDER";
				}

				const num = parseFloat(value.replace(",", "."));
				if (isNaN(num) || num <= 0) {
					context.addIssue({
						code: z.ZodIssueCode.custom,
						message: "Valor deve ser maior que 0",
						path: ["paymentValue"],
					});
					return z.NEVER;
				}
				return num;
			}),
		installments: z.string().optional(),
	})
	.superRefine((data, ctx) => {
		if (data.paymentMethod === PaymentMethodEnum.CreditCard) {
			if (!data.installments || data.installments.trim() === "") {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: "Número de parcelas é obrigatório para pagamento com cartão",
					path: ["installments"],
				});
			} else {
				const numInstallments = parseInt(data.installments, 10);
				if (isNaN(numInstallments) || numInstallments <= 0) {
					ctx.addIssue({
						code: z.ZodIssueCode.custom,
						message: "Número de parcelas deve ser maior que 0",
						path: ["installments"],
					});
				}
			}
		}
	});

type PaymentFormInputs = z.infer<typeof PaymentFormSchema>;

type FieldProps<T> = {
	value?: T;
	onChange: (value: T) => void;
	error?: string;
};

const PaymentMethodSelect: React.FC<FieldProps<PaymentMethodEnum>> = ({ value, onChange, error }) => (
	<div className="relative">
		<Label className="absolute top-[-5px] bg-white px-2 left-3">Forma de pagamento</Label>
		<Select onValueChange={val => onChange(parseInt(val, 10) as PaymentMethodEnum)} value={value?.toString() ?? ""}>
			<SelectTrigger className=" w-full sm:w-[220px] lg:w-[190px] 2xl:w-[220px] h-[45px] border-2 text-gray-400 rounded-[15px] border-[#505050]">
				{value ? PaymentMethodData.find(item => item.value === value)?.label : <span className="text-gray-400">Selecione</span>}
			</SelectTrigger>
			<SelectContent>
				{PaymentMethodData.map(item => (
					<SelectItem key={item.value} value={item.value.toString()}>
						{item.label}
					</SelectItem>
				))}
			</SelectContent>
		</Select>
		{error && <p className="absolute -bottom-4 left-3 text-red-500 text-xs">{error}</p>}
	</div>
);

const PaymentValueInput: React.FC<FieldProps<string>> = ({ value, onChange, error }) => (
	<div className="relative">
		<Label className="absolute top-[-5px] bg-white px-1 left-3">Valor</Label>
		<NumericFormat
			value={value}
			onValueChange={values => onChange(values.value)}
			thousandSeparator="."
			decimalSeparator=","
			prefix="R$ "
			placeholder="R$ 0,00"
			customInput={Input}
			className="border-2 h-[45px] text-gray-400 rounded-[15px] border-[#505050]"
		/>
		{error && <p className="absolute -bottom-4 left-3 text-red-500 text-xs">{error}</p>}
	</div>
);

const InstallmentsSelect: React.FC<FieldProps<string>> = ({ value, onChange, error }) => (
	<div className="relative flex items-end">
		<Label className="absolute top-[-5px] bg-white px-2 left-3">N° Parcelas</Label>
		<Select onValueChange={onChange} value={value}>
			<SelectTrigger className="w-full h-[45px] border-2 text-gray-400 rounded-[15px] border-[#505050]">
				<SelectValue placeholder="Selecione" />
			</SelectTrigger>
			<SelectContent>
				{Array.from({ length: 12 }, (_, i) => (
					<SelectItem key={i + 1} value={(i + 1).toString()}>
						{i + 1}
					</SelectItem>
				))}
			</SelectContent>
		</Select>
		{error && <p className="absolute -bottom-4 left-3 text-red-500 text-xs">{error}</p>}
	</div>
);

const PaymentForm: React.FC = () => {
	const {
		control,
		handleSubmit,
		watch,
		reset,
		formState: { errors },
		setError,
		clearErrors,
	} = useForm<PaymentFormInputs>({
		resolver: zodResolver(PaymentFormSchema),
		defaultValues: {
			paymentMethod: undefined,
			paymentValue: undefined,
			installments: undefined,
		},
	});

	const selectedPayment = watch("paymentMethod");
	const paymentValue = watch("paymentValue");
	const orderId = useAtomValue(orderIdAtom);
	const orderInfo = useAtomValue(orderInfoAtom);

	const { addPaymentMutation } = useAddPayment();

	useEffect(() => {
		if (selectedPayment !== PaymentMethodEnum.Cash && paymentValue && paymentValue !== "TOTAL_ORDER" && orderInfo) {
			const numValue = Number(paymentValue);

			if (!isNaN(numValue) && numValue > +orderInfo.total) {
				setError("paymentValue", {
					message: "Valor não pode ser maior que o total do pedido",
				});
			} else {
				clearErrors("paymentValue");
			}
		} else {
			clearErrors("paymentValue");
		}
	}, [selectedPayment, paymentValue, orderInfo, setError, clearErrors]);

	const onSubmit = (data: PaymentFormInputs) => {
		if (orderId !== null) {
			const finalValue = data.paymentValue === "TOTAL_ORDER" ? orderInfo?.total : data.paymentValue;

			addPaymentMutation.mutate({
				orderId: orderId,
				item: {
					paymentMethodId: data.paymentMethod,
					value: Number(finalValue),
					parcel: data.installments ? Number(data.installments) : 1,
				},
			});
			reset({
				paymentMethod: undefined,
				paymentValue: undefined,
				installments: undefined,
			});
		} else {
			reset({
				paymentMethod: undefined,
				paymentValue: undefined,
				installments: undefined,
			});
		}
		reset({
			paymentMethod: undefined,
			paymentValue: undefined,
			installments: undefined,
		});
	};

	return (
		<form
			onSubmit={handleSubmit(onSubmit)}
			className="flex flex-col sm:flex-row lg:flex-col gap-2 2xl:flex-row 2xl:gap-2 2xl:pb-2 justify-center items-end"
		>
			<div className="flex flex-col w-full sm:flex-row 2xl:flex-col gap-2">
				<div className="flex flex-col  w-full sm:w-auto sm:flex-row sm:items-end gap-2">
					<Controller
						control={control}
						name="paymentMethod"
						render={({ field }) => (
							<PaymentMethodSelect value={field.value} onChange={field.onChange} error={errors.paymentMethod?.message} />
						)}
					/>
					<Controller
						control={control}
						name="paymentValue"
						render={({ field }) => (
							<PaymentValueInput
								value={field.value ? field.value.toString() : ""}
								onChange={field.onChange}
								error={errors.paymentValue?.message}
							/>
						)}
					/>
				</div>
				{Number(selectedPayment) === PaymentMethodEnum.CreditCard && (
					<Controller
						control={control}
						name="installments"
						render={({ field }) => (
							<InstallmentsSelect value={field.value} onChange={field.onChange} error={errors.installments?.message} />
						)}
					/>
				)}
			</div>
			<motion.div
				transition={{ duration: 0.3, ease: "anticipate" }}
				className="flex w-full sm:w-auto  2xl:w-auto lg:w-full justify-center items-center h-full"
			>
				<Button
					type="submit"
					className="bg-mainColor  2xl:w-auto w-full  h-[45px] shadow-[0_4px_4px_0_rgba(0,0,0,0.25)] gap-2 flex text-white px-3 py-1 rounded-[15px]"
				>
					<Plus size={18} />
					Adicionar
				</Button>
			</motion.div>
		</form>
	);
};

export default PaymentForm;
