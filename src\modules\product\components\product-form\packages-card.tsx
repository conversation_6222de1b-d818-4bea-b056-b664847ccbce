import { CardLMPContainer } from "@/shared/components/custom/card";
import { Button } from "@/shared/components/ui/button";
import { motion } from "framer-motion";
import { Package, PackagePlus, Trash2 } from "lucide-react";
import { JSX } from "react";
import type { UseFieldArrayReturn, UseFormReturn } from "react-hook-form";
import type { ProductFormData } from "../../validators/product/product-form.schema";
import { InputField } from "./input-field";

type PackagesCardProps = Readonly<{
	methods: UseFormReturn<ProductFormData>;
	packageFields: UseFieldArrayReturn<ProductFormData, "packages">;
	isEditMode: boolean;
}>;

const getDefaultPackage = (): ProductFormData["packages"][number] => ({
	name: "",
	barcode: "",
	code: "",
	quantityPerPackage: 0,
});

export const PackagesCard = ({ methods, packageFields, isEditMode }: PackagesCardProps): JSX.Element => {
	const { fields, remove, append } = packageFields;

	const handleAddPackage = (): void => {
		append(getDefaultPackage());
	};

	const canAddPackage: boolean = isEditMode || fields.length === 0;

	const handleAddPackageKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>): void => {
		if (event.key === "Enter" || event.key === " ") {
			event.preventDefault();
			handleAddPackage();
		}
	};

	return (
		<CardLMPContainer
			icon={<PackagePlus size={22} className="text-gray-500" />}
			title="Embalagens"
			description="Configure os diferentes tipos de embalagens do produto"
		>
			<div className="space-y-6">
				{fields.map((field, index) => (
					<motion.div
						key={field.id}
						initial={{ opacity: 0, y: -20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -20 }}
						className="relative p-6 bg-gray-50/50 rounded-xl border border-gray-200"
					>
						<div className="absolute -top-3 left-4 bg-white px-2 text-sm font-medium text-gray-700">
							<div className="flex items-center gap-2">
								<span className="p-1 bg-mainColor/10 rounded-lg">
									<Package size={14} className="text-mainColor" />
								</span>
								<span>Embalagem {index + 1}</span>
							</div>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-5">
							<InputField
								label="Nome"
								name={`packages.${index}.name`}
								placeholder="Ex: Caixa Padrão"
								icon={<Package size={16} />}
								methods={methods}
								required
							/>
							<InputField
								label="Código de Barras"
								name={`packages.${index}.barcode`}
								placeholder="Ex: 987654321"
								icon={<PackagePlus size={16} />}
								methods={methods}
								required
							/>
							<InputField
								label="Código"
								name={`packages.${index}.code`}
								placeholder="Ex: PKG001"
								icon={<Package size={16} />}
								methods={methods}
								required
							/>
							<InputField
								label="Quantidade por Embalagem"
								name={`packages.${index}.quantityPerPackage`}
								type="number"
								placeholder="Ex: 10"
								icon={<PackagePlus size={16} />}
								methods={methods}
								required
							/>
						</div>
						{fields.length > 0 && (
							<motion.button
								type="button"
								onClick={(): void => remove(index)}
								className="absolute -top-3 right-3 p-2 bg-white text-red-500 hover:bg-red-50 rounded-lg transition-colors shadow-sm border border-red-100"
								whileHover={{ scale: 1.05 }}
								whileTap={{ scale: 0.95 }}
							>
								<Trash2 size={16} />
							</motion.button>
						)}
					</motion.div>
				))}
				{fields.length === 0 && (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						className="bg-gray-50/50 rounded-xl p-8 border border-gray-200 text-center"
					>
						<PackagePlus size={40} className="mx-auto text-mainColor/60 mb-3" />
						<p className="text-gray-500 text-sm">Nenhuma embalagem configurada. Clique em "Adicionar Embalagem" para começar.</p>
					</motion.div>
				)}
			</div>

			{canAddPackage && (
				<div className="flex items-center justify-center mt-4">
					<Button
						type="button"
						onClick={handleAddPackage}
						onKeyDown={handleAddPackageKeyDown}
						tabIndex={0}
						aria-label="Adicionar nova embalagem"
						className="flex items-center gap-2 bg-mainColor text-white px-4 py-2 rounded-lg text-sm hover:bg-mainColor/90 transition-colors"
						disabled={!canAddPackage}
					>
						<PackagePlus size={16} />
						Adicionar Embalagem
					</Button>
				</div>
			)}
		</CardLMPContainer>
	);
};
