import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IInvalidateInvoiceResponse } from "../../dtos/invoice.dto";
import { ORDERS_ENDPOINTS } from "../endpoints";

export const invalidateInvoiceRequest = async (
  id: number,
  body: any
): Promise<ApiResponse<IInvalidateInvoiceResponse>> => {
  return createRequest({
    path: ORDERS_ENDPOINTS.INVOICE_INVALIDATE(id),
    method: "PATCH",
    body,
  });
};
