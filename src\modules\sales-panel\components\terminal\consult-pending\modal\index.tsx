import { useConsultPendingModal } from "@/modules/sales-panel/hooks/consult-pending/use-consult-pending-modal.hook";
import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { motion } from "framer-motion";
import { LucideShoppingCart, LucideX } from "lucide-react";
import { Pagination } from "../../../../../../shared/components/custom/pagination";
import { FilterForm } from "./filter-form";
import { PendingOrdersList } from "./pending-orders-list";

interface ConsultPendingModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export const ConsultPendingModal = ({ isOpen, onClose }: ConsultPendingModalProps) => {
	const { data, isLoading, currentOrderId, page, itemsPerPage, totalPages, form, handlePageChange, handleItemsPerPageChange, handleSelectOrder } =
		useConsultPendingModal(isOpen);

	return (
		<OverlayContainer isVisible={isOpen} onClose={onClose}>
			<motion.div
				onClick={e => e.stopPropagation()}
				role="dialog"
				aria-modal="true"
				aria-label="Consulta de Pedidos Pendentes"
				className="flex flex-col w-full max-w-3xl h-[80vh] bg-white rounded-[15px] shadow-lg overflow-hidden"
				initial={{ opacity: 0, scale: 0.95 }}
				animate={{ opacity: 1, scale: 1 }}
				exit={{ opacity: 0, scale: 0.95 }}
				transition={{ duration: 0.2, ease: "easeOut" }}
			>
				<div className="flex items-center justify-between p-4">
					<h3 className="text-lg font-semibold text-[#505050] flex items-center gap-2">
						<LucideShoppingCart size={20} />
						Pedidos Pendentes
					</h3>
					<button
						className="font-semibold text-[#505050] hover:text-white hover:bg-cyan-700 p-2 rounded-full transition-colors"
						onClick={onClose}
					>
						<LucideX size={20} />
					</button>
				</div>
				<div className="p-4 flex flex-col gap-4 h-full overflow-hidden">
					<FilterForm form={form} />
					<div className="overflow-y-auto border-t-1 border-gray-300 flex-1 min-h-0">
						<PendingOrdersList
							orders={data?.success ? data.data.data : []}
							currentOrderId={currentOrderId}
							onSelectOrder={order => {
								handleSelectOrder(order.id);
								onClose();
							}}
							isLoading={isLoading}
						/>
					</div>

					{data?.success && data.data.data.length > 0 && (
						<Pagination
							page={page}
							totalPages={totalPages}
							itemsPerPage={itemsPerPage}
							onPageChange={handlePageChange}
							onItemsPerPageChange={handleItemsPerPageChange}
						/>
					)}
				</div>
			</motion.div>
		</OverlayContainer>
	);
};
