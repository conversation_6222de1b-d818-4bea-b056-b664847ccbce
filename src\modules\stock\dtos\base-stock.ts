import { ICategory } from "@/modules/product/types/category.type";

export interface IInvoiceBase {
	key: string;
	issueDate: string;
}

export interface IProductPackage {
	name: string;
	barcode: string;
	code: string;
	quantityPerPackage: number;
	id?: number;
}

export interface IProductBase {
	id?: number;
	name: string;
	barcode: string;
	supplierCode: string;
	category: ICategory;
	price: number;
	ncm: string;
	costPrice: number;
}
