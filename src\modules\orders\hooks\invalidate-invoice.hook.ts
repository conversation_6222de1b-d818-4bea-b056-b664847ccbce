import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { invalidateInvoiceRequest } from "../api/requests/invalidate-invoice";

export interface IUseInvalidateInvoiceMutationProps {
  onSuccess?: () => void;
}

export interface InvalidateInvoiceInput {
  id: number;
  justification: string;
}

export const useInvalidateInvoiceMutation = ({ onSuccess }: IUseInvalidateInvoiceMutationProps = {}) => {
  const invalidateInvoiceMutation = useMutation({
    mutationFn: async ({ id, justification }: InvalidateInvoiceInput) => {
      const response = await invalidateInvoiceRequest(id, { justification });
      if (response.success) {
        return response.data;
      }
      throw new Error(response.data.message);
    },
    onMutate: () => {
      const pendingToastId = toast.loading("Invalidando nota fiscal...");
      return { pendingToastId };
    },
    onSuccess: (data, _, context) => {
      if (context?.pendingToastId) {
        toast.success(data.message, { id: context.pendingToastId });
      }
      onSuccess?.();
    },
    onError: (error: Error, _, context) => {
      if (context?.pendingToastId) {
        toast.error(error.message, { id: context.pendingToastId });
      }
    },
  });

  return {
    invalidateInvoice: invalidateInvoiceMutation.mutateAsync,
    isLoading: invalidateInvoiceMutation.isPending,
    invalidateInvoiceMutation,
  };
};
