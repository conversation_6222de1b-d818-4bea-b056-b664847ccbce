import { IItemSidebar } from "@/layout/types/sidebar-active-item.type";
import { LayoutGrid } from "lucide-react";
import { DASHBOARD_PATHS } from "./routes-paths.data";

export const DASHBOARD_SUBITEM_IDS = {
	DASHBOARD: "dashboard",
} as const;

export const DASHBOARD_CONFIG: IItemSidebar = {
	id: "dashboard-group",
	moduleActivated: true,
	label: "Dashboard",
	accessibleDescription: "ícone da dashboard",
	Icon: LayoutGrid,
	type: "menu",
	path: DASHBOARD_PATHS.DASHBOARD,
	subItems: [
		{
			id: DASHBOARD_SUBITEM_IDS.DASHBOARD,
			label: "Dashboard",
			Icon: LayoutGrid,
			accessibleDescription: "ícone da dashboard",
			path: DASHBOARD_PATHS.DASHBOARD,
		},
	],
};
