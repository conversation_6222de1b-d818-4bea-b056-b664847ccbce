import { CardLMPContainer } from "@/shared/components/custom/card";
import { AnimatePresence, motion } from "framer-motion";
import { AlertCircle, ImageIcon, X } from "lucide-react";
import { JSX } from "react";
import { UseFormReturn } from "react-hook-form";
import { useDeleteProductImage } from "../../hooks/product/delete-product-image.hook";
import { ProductFormData } from "../../validators/product/product-form.schema";

interface ProductImagesCardProps {
	methods: UseFormReturn<ProductFormData>;
	isInEditMode?: boolean;
	isLoadingImage?: boolean;
	imageError?: string | null;
	productId: number;
	handleImageUpload: (file: File) => Promise<void>;
}

export const ProductImagesCard: React.FC<ProductImagesCardProps> = ({
	methods,
	isInEditMode = false,
	isLoadingImage = false,
	imageError = null,
	productId,
	handleImageUpload,
}): JSX.Element => {
	const { deleteProductImage } = useDeleteProductImage();
	const images = methods.watch("images") || [];

	const handleDrop = (e: React.DragEvent<HTMLDivElement>): void => {
		e.preventDefault();
		e.stopPropagation();

		const files = e.dataTransfer.files;
		if (files && files.length > 0) {
			const file = files[0];
			if (file.type.startsWith("image/")) {
				handleImageUpload(file);
			}
		}
	};

	const handleDragOver = (e: React.DragEvent<HTMLDivElement>): void => {
		e.preventDefault();
		e.stopPropagation();
	};

	return (
		<CardLMPContainer
			icon={<ImageIcon size={22} className="text-gray-500" />}
			title="Imagens do Produto"
			description="Adicione uma imagem do produto"
		>
			<AnimatePresence mode="popLayout">
				{isLoadingImage && (
					<div className="flex flex-col items-center justify-center p-8 bg-gray-50/50 rounded-lg border border-gray-200">
						<div className="w-10 h-10 border-2 border-mainColor border-t-transparent rounded-full animate-spin mb-4"></div>
						<p className="text-gray-600 text-sm font-medium">Carregando imagem do produto...</p>
					</div>
				)}

				{imageError && !isLoadingImage && (
					<motion.div
						className="flex flex-col w-full h-full items-center justify-center p-6 bg-red-50 rounded-lg border border-red-200"
						initial={{ opacity: 0, scale: 0.95 }}
						animate={{ opacity: 1, scale: 1 }}
						exit={{ opacity: 0, scale: 0.95 }}
						transition={{ duration: 0.18, ease: "easeInOut" }}
					>
						<div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-full mb-3">
							<AlertCircle size={24} className="text-red-500" />
						</div>
						<p className="text-red-700 text-sm font-medium mb-1">Erro ao carregar imagem</p>
						<p className="text-red-600/80 text-xs text-center mb-3 break-all">{imageError}</p>
						<motion.button
							type="button"
							className="px-4 py-2 bg-red-100 text-red-700 rounded-lg text-xs font-medium hover:bg-red-200 transition-colors"
							whileHover={{ scale: 1.05 }}
							whileTap={{ scale: 0.95 }}
							onClick={() => document.getElementById("image-upload")?.click()}
						>
							Selecionar nova imagem
						</motion.button>
					</motion.div>
				)}

				{images.length > 0 && !isLoadingImage && !imageError ? (
					<motion.div
						className="grid grid-cols-1 gap-4"
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						exit={{ opacity: 0 }}
						transition={{ duration: 0.18, ease: "easeInOut" }}
					>
						{images.map((image, index) => (
							<motion.div
								key={`image-${image.name}-${image.size}`}
								className="relative group"
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								exit={{ opacity: 0 }}
								transition={{ duration: 0.2 }}
							>
								<div className="relative overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-all duration-300 h-64 w-full flex items-center justify-center bg-white">
									<img
										src={URL.createObjectURL(image)}
										alt={`Preview ${index + 1}`}
										className="w-full h-full object-contain transition-transform duration-500 group-hover:scale-105"
									/>
									<div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-between p-3">
										<div className="text-white text-xs font-medium">Imagem {index + 1}</div>
										<motion.button
											type="button"
											onClick={async () => {
												if (isInEditMode && productId) {
													await deleteProductImage(productId);
													const newImages = [...images];
													newImages.splice(index, 1);
													methods.setValue("images", newImages);
												} else {
													const newImages = [...images];
													newImages.splice(index, 1);
													methods.setValue("images", newImages);
												}
											}}
											className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-all duration-300 shadow-lg"
											whileHover={{ scale: 1.1, rotate: 90 }}
											whileTap={{ scale: 0.9 }}
										>
											<X size={18} />
										</motion.button>
									</div>
								</div>
							</motion.div>
						))}
					</motion.div>
				) : (
					!isLoadingImage &&
					!imageError && (
						<motion.div
							className="relative border-2 border-dashed border-gray-200 rounded-lg p-8 text-center hover:border-mainColor transition-all duration-300 cursor-pointer bg-gray-50/50 h-64 flex flex-col items-center justify-center group hover:bg-gray-50 hover:shadow-md"
							onClick={() => document.getElementById("image-upload")?.click()}
							onDrop={handleDrop}
							onDragOver={handleDragOver}
							initial={{ opacity: 0, scale: 0.95 }}
							animate={{ opacity: 1, scale: 1 }}
							exit={{ opacity: 0, scale: 0.95 }}
							transition={{ duration: 0.18, ease: "easeInOut" }}
						>
							<input
								id="image-upload"
								type="file"
								accept="image/*"
								onChange={e => {
									const files = e.target.files;
									if (files && files.length > 0) {
										handleImageUpload(files[0]);
									}
								}}
								className="hidden"
							/>
							<motion.div
								className="w-16 h-16 rounded-full bg-mainColor/10 flex items-center justify-center group-hover:bg-mainColor/20 transition-all duration-300"
								whileHover={{ scale: 1.1 }}
								whileTap={{ scale: 0.9 }}
							>
								<ImageIcon className="w-8 h-8 text-mainColor transition-transform duration-300 group-hover:rotate-12" />
							</motion.div>
							<p className="text-lg font-medium text-gray-700 mt-4 group-hover:text-mainColor transition-colors duration-300">
								Arraste ou clique para adicionar imagem
							</p>
							<p className="text-sm text-gray-500 mt-2">PNG, JPG ou JPEG (max. 5MB)</p>
						</motion.div>
					)
				)}
			</AnimatePresence>
		</CardLMPContainer>
	);
};
