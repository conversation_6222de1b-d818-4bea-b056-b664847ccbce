import { use<PERSON>et<PERSON><PERSON> } from "jotai";
import Cookies from "js-cookie";
import { LMPStore } from "@/shared/providers/global.provider";
import { authStateAtom } from "../states/auth.state";
import { userProfileAtom } from "../states/user.state";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";

export const useLogout = () => {
	const setAuthState = useSetAtom(authStateAtom);
	const setUserProfile = useSetAtom(userProfileAtom);
	const navigate = useNavigate();
	const queryClient = useQueryClient();

	const logout = () => {
		try {
			Cookies.remove("access_token");
			Cookies.remove("refresh_token");

			LMPStore.set(authStateAtom, { isAuthenticated: false });
			LMPStore.set(userProfile<PERSON>tom, null);

			localStorage.removeItem("authState");
			localStorage.removeItem("userProfile");

			const keysToRemove = [];
			for (let i = 0; i < localStorage.length; i++) {
				const key = localStorage.key(i);
				if (key && (key.includes("auth") || key.includes("user") || key.includes("token"))) {
					keysToRemove.push(key);
				}
			}
			keysToRemove.forEach(key => localStorage.removeItem(key));
			sessionStorage.clear();
			queryClient.clear();
			setAuthState({ isAuthenticated: false });
			setUserProfile(null);

			navigate({ to: "/" });
			toast.success("Logout realizado com sucesso!");
		} catch (error) {
			console.error("Erro durante logout:", error);
			toast.error("Erro ao fazer logout. Tente novamente.");
		}
	};

	return { logout };
};
