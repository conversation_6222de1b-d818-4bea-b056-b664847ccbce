import { LogoSvg } from "@/shared/assets/logo/logo";
import ImageBubble from "@/shared/assets/utils/bubble.webp";
import { LoginForm } from "./login-form";
import { LoginDescription } from "./login.description";

const LoginPage = () => {
	return (
		<div className="min-h-screen w-full bg-gradient-to-br from-gray-50 via-blue-50 to-gray-100 flex items-center justify-center p-3 sm:p-4 relative overflow-hidden">
			<div className="lg:hidden absolute inset-0 overflow-hidden">
				<div className="absolute -top-20 -right-20 w-40 h-40 bg-[#0197B2]/10 rounded-full"></div>
				<div className="absolute -bottom-16 -left-16 w-32 h-32 bg-[#0197B2]/5 rounded-full"></div>
				<div className="absolute top-1/4 -left-8 w-16 h-16 bg-[#0197B2]/8 rounded-full"></div>
				<div className="absolute bottom-1/3 -right-12 w-24 h-24 bg-[#0197B2]/6 rounded-full"></div>
			</div>

			<div className="w-full max-w-6xl bg-white/95 backdrop-blur-sm rounded-2xl sm:rounded-3xl shadow-2xl overflow-hidden animate-in fade-in duration-700 relative z-10">
				<div className="flex flex-col lg:flex-row min-h-[550px] sm:min-h-[600px]">
					<div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-[#0197B2] via-[#0197B2] to-[#016d85] lg:p-12 flex-col justify-center items-center text-white relative overflow-hidden">
						<div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
						<div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
						<div className="absolute top-1/2 left-1/4 w-2 h-2 bg-white/30 rounded-full"></div>
						<div className="absolute top-1/3 right-1/4 w-1 h-1 bg-white/40 rounded-full"></div>{" "}
						<div className="text-center z-10 space-y-6">
							<div className="flex justify-center mb-8">
								<LogoSvg />
							</div>
							<div className="space-y-4">
								<h1 className="text-4xl lg:text-5xl font-bold tracking-tight">LMP Gestão!</h1>
								<p className="text-lg lg:text-xl text-white/90 max-w-md mx-auto leading-relaxed">
									Produtos de limpeza para sua casa ou negócio.
								</p>
								<div className="w-20 h-1 bg-white/40 rounded-full mx-auto mt-6"></div>
							</div>
						</div>{" "}
					</div>{" "}
					<div className="w-full lg:w-1/2 p-6 sm:p-8 lg:p-12 flex flex-col justify-center relative">
						{" "}
						<div className="lg:hidden text-center mb-8">
							<div className="flex justify-center mb-6">
								<div className="flex flex-col items-center space-y-3">
									<div className="w-16 h-16 bg-gradient-to-br from-[#0197B2] to-[#016d85] rounded-2xl flex items-center justify-center shadow-lg">
										<img src={ImageBubble} alt="Logo" className="w-8 h-8" />
									</div>
								</div>
							</div>
							<div className="space-y-3">
								<h1 className="text-3xl font-bold text-gray-800 tracking-tight">Bem-vindo de volta!</h1>
								<p className="text-gray-600 text-base">Faça login para acessar sua conta</p>
								<div className="w-16 h-1 bg-gradient-to-r from-[#0197B2] to-[#016d85] rounded-full mx-auto"></div>
							</div>
						</div>
						<div className="max-w-md mx-auto w-full space-y-6 sm:space-y-8">
							<LoginDescription />
							<LoginForm />
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default LoginPage;
