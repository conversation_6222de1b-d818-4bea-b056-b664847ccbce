import { Bell, Calendar, Clock, LogOut, Search, Settings, User } from "lucide-react";

import { useUserProfile } from "@/modules/auth/hooks/user-profile.hook";
import { useLogout } from "@/modules/auth/hooks/logout.hook";
import { Popover, PopoverContent, PopoverTrigger } from "@/shared/components/ui/popover";
import { motion } from "framer-motion";
import { useState, useEffect } from "react";

const Notification = () => {
	const [notificationCount, setNotificationCount] = useState<number>(0);
	const [showNotifications, setShowNotifications] = useState<boolean>(false);

	const handleNotificationClick = () => {
		setShowNotifications(!showNotifications);
		if (notificationCount > 0) {
			setNotificationCount(0);
		} else {
			setNotificationCount(prevCount => prevCount + 1);
		}
	};

	return (
		<li className="flex items-center gap-2 relative">
			<Popover open={showNotifications} onOpenChange={setShowNotifications}>
				<PopoverTrigger asChild>
					<motion.div
						whileTap={{ scale: 0.8 }}
						whileHover={{ scale: 1.1 }}
						transition={{ type: "spring", stiffness: 300, damping: 15 }}
						className="relative p-2 rounded-full hover:bg-gray-100"
					>
						<Bell
							onClick={handleNotificationClick}
							className={`text-gray-600 hover:text-mainColor cursor-pointer ${notificationCount > 0 ? "text-red-500" : ""}`}
							size={20}
						/>
						{notificationCount > 0 && (
							<motion.span
								initial={{ scale: 0, opacity: 0 }}
								animate={{ scale: 1, opacity: 1 }}
								transition={{ type: "spring", stiffness: 400, damping: 20 }}
								className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full shadow-md"
							>
								{notificationCount}
							</motion.span>
						)}
					</motion.div>
				</PopoverTrigger>
				<PopoverContent className="w-[320px] p-0 mr-4">
					<div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-md p-3 border-b border-gray-200">
						<h3 className="font-semibold text-gray-800 flex items-center gap-2">
							<Bell size={16} className="text-mainColor" />
							Notificações
						</h3>
					</div>
					<div className="max-h-[300px] overflow-y-auto">
						{notificationCount > 0 ? (
							<div className="p-3 hover:bg-gray-50 border-b border-gray-100 cursor-pointer transition-colors">
								<div className="flex items-start gap-3">
									<div className="bg-blue-100 p-2 rounded-full">
										<Calendar size={16} className="text-mainColor" />
									</div>
									<div>
										<p className="text-sm font-medium text-gray-800">Nova transação registrada</p>
										<p className="text-xs text-gray-500">Uma nova venda foi registrada no sistema</p>
										<div className="flex items-center gap-1 mt-1">
											<Clock size={12} className="text-gray-400" />
											<span className="text-xs text-gray-400">Agora mesmo</span>
										</div>
									</div>
								</div>
							</div>
						) : (
							<div className="p-6 text-center">
								<div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
									<Bell size={20} className="text-gray-400" />
								</div>
								<p className="text-gray-600 text-sm">Você não tem notificações no momento</p>
							</div>
						)}
					</div>
					<div className="p-2 border-t border-gray-200 bg-gray-50 rounded-b-md">
						<button className="text-xs text-center w-full text-mainColor hover:text-blue-700 font-medium py-1">
							Ver todas as notificações
						</button>
					</div>
				</PopoverContent>
			</Popover>
		</li>
	);
};

export const Header = () => {
	const { profile } = useUserProfile();
	const { logout } = useLogout();
	const [currentDate] = useState<string>(new Date().toLocaleDateString("pt-BR", { day: "numeric", month: "long", year: "numeric" }));
	const [isScrolled, setIsScrolled] = useState<boolean>(false);

	useEffect(() => {
		const mainScrollableArea = document.getElementById("main-scrollable-area");

		if (!mainScrollableArea) return;

		const handleScroll = () => {
			const scrollTop = mainScrollableArea.scrollTop;
			setIsScrolled(scrollTop > 0);
		};

		mainScrollableArea.addEventListener("scroll", handleScroll);

		return () => {
			mainScrollableArea.removeEventListener("scroll", handleScroll);
		};
	}, []);

	const handleLogout = () => {
		logout();
	};

	return (
		<header
			className={`w-full z-50 sticky top-0 px-6 py-3 hidden sm:flex justify-between items-center bg-white bg-opacity-95 backdrop-blur-sm rounded-[20px] transition-all duration-300 hover:shadow-xl ${
				isScrolled ? "shadow-lg shadow-black/10" : "shadow-lg"
			}`}
		>
			<div className="flex items-center gap-4">
				<div>
					<motion.h1
						initial={{ opacity: 0, y: -10 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.3 }}
						className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent"
					>
						Bem vindo de volta, {profile?.name || "Jheison"}!
					</motion.h1>
					<p className="text-sm text-gray-500 flex items-center gap-2">
						<span className="inline-block">Produtos de limpeza para sua casa ou negócio.</span>
						<span className="h-1 w-1 rounded-full bg-gray-300"></span>
						<span className="text-xs font-medium text-gray-400">{currentDate}</span>
					</p>
				</div>
			</div>

			<div className="flex items-center gap-6">
				<ul className="flex gap-2">
					<li className="flex items-center">
						<div className="relative group">
							<motion.div
								whileTap={{ scale: 0.9 }}
								whileHover={{ scale: 1.1 }}
								transition={{ type: "spring", stiffness: 300 }}
								className="p-2 rounded-full hover:bg-gray-100"
							>
								<Search className="text-gray-600 hover:text-mainColor cursor-pointer" size={20} />
								<div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
									Pesquisar
								</div>
							</motion.div>
						</div>
					</li>
					<Notification />
					<li className="flex items-center">
						<div className="relative group">
							<motion.div
								whileTap={{ scale: 0.9 }}
								whileHover={{ scale: 1.1 }}
								transition={{ type: "spring", stiffness: 300 }}
								className="p-2 rounded-full hover:bg-gray-100"
							>
								<Settings className="text-gray-600 hover:text-mainColor cursor-pointer" size={20} />
								<div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
									Configurações
								</div>
							</motion.div>
						</div>
					</li>
				</ul>

				<div className="h-6 w-px bg-gray-200"></div>

				<Popover>
					<PopoverTrigger asChild>
						<motion.div
							whileHover={{ scale: 1.03 }}
							transition={{ type: "spring", stiffness: 300 }}
							className="flex items-center gap-3 cursor-pointer p-1.5 rounded-full transition-colors duration-200 hover:bg-gray-50 pr-3 border border-transparent hover:border-gray-200"
						>
							<div className="relative">
								<div className="w-10 h-10 rounded-full overflow-hidden ring-2 ring-offset-2 ring-mainColor">
									<img
										src="https://png.pngtree.com/thumb_back/fh260/background/20230615/pngtree-man-wearing-a-pair-of-yellow-sunglasses-in-front-of-a-image_2898170.jpg"
										alt="Avatar"
										className="w-full h-full object-cover"
									/>
								</div>
								<motion.span
									animate={{ scale: [1, 1.2, 1], opacity: [0.7, 1, 0.7] }}
									transition={{ duration: 2, repeat: Infinity }}
									className="absolute bottom-0 right-0 w-3 h-3 bg-green-400 border-2 border-white rounded-full"
								></motion.span>
							</div>
							<div className="text-left flex flex-col">
								<span className="text-gray-800 font-semibold text-sm">{profile?.name || "Usuário"}</span>
								<span className="text-gray-500 text-xs">Administrador</span>
							</div>
						</motion.div>
					</PopoverTrigger>
					<PopoverContent className="w-[250px] mr-2 rounded-[15px] p-0">
						<div className="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
							<div className="flex items-center gap-2">
								<div className="w-10 h-10 rounded-full overflow-hidden ring-1 ring-mainColor">
									<img
										src="https://png.pngtree.com/thumb_back/fh260/background/20230615/pngtree-man-wearing-a-pair-of-yellow-sunglasses-in-front-of-a-image_2898170.jpg"
										alt="Avatar"
										className="w-full h-full object-cover"
									/>
								</div>
								<div>
									<h3 className="font-medium text-sm text-gray-800">{profile?.name || "Usuário"}</h3>
									<p className="text-xs text-gray-500"><EMAIL></p>
								</div>
							</div>
						</div>
						<div className="py-1">
							<button
								className="w-full px-3 py-2 text-sm text-gray-400 cursor-not-allowed flex items-center gap-2"
								disabled
								title="Funcionalidade em desenvolvimento"
							>
								<User size={16} className="text-gray-400" />
								Meu Perfil
							</button>
							<button
								onClick={handleLogout}
								className="w-full px-3 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-600 flex items-center gap-2 transition-colors duration-200"
							>
								<LogOut size={16} className="text-red-500" />
								Sair
							</button>
						</div>
					</PopoverContent>
				</Popover>
			</div>
		</header>
	);
};
