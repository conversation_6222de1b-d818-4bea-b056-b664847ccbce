import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { Button } from "@/shared/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import { useAtom } from "jotai";
import { Download, Eye, FileText, Printer, X } from "lucide-react";
import { useEffect, useState } from "react";
import { pdfViewerState } from "../states/pdf-viewer.state";

function useIsMobile() {
	const [isMobile, setIsMobile] = useState(false);

	useEffect(() => {
		if (typeof window !== "undefined") {
			setIsMobile(/Mobi|Android/i.test(window.navigator.userAgent));
		}
	}, []);

	return isMobile;
}

export function PdfViewerModal() {
	const [pdfFile, setPdfFile] = useAtom(pdfViewerState);
	const [pdfUrl, setPdfUrl] = useState<string | null>(null);
	const isMobile = useIsMobile();
	const open = Boolean(pdfFile);
	const onClose = () => setPdfFile(null);

	useEffect(() => {
		if (!pdfFile) return;

		const blob = pdfFile instanceof Blob ? pdfFile : new Blob([pdfFile], { type: "application/pdf" });
		const url = URL.createObjectURL(blob);
		setPdfUrl(url);

		return () => {
			URL.revokeObjectURL(url);
			setPdfUrl(null);
		};
	}, [pdfFile]);

	const handleDownload = () => {
		if (pdfUrl) {
			const link = document.createElement("a");
			link.href = pdfUrl;
			link.download = "cupom-fiscal.pdf";
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		}
	};

	const handlePrint = () => {
		if (pdfUrl) {
			const iframe = document.createElement("iframe");
			iframe.style.display = "none";
			iframe.src = pdfUrl;

			iframe.onload = () => {
				if (iframe.contentWindow) {
					iframe.contentWindow.print();
					document.body.removeChild(iframe);
				}
			};

			document.body.appendChild(iframe);
		}
	};

	if (!pdfFile) return null;

	return (
		<OverlayContainer isVisible={open} onClose={onClose}>
			<motion.div
				className="bg-white rounded-xl m-4 w-[90vw] h-[90vh] shadow-2xl flex flex-col overflow-hidden border border-gray-200"
				role="dialog"
				aria-modal="true"
				aria-label="Visualizador de PDF - Cupom Fiscal"
				onClick={e => e.stopPropagation()}
				initial={{ opacity: 0, scale: 0.95 }}
				animate={{ opacity: 1, scale: 1 }}
				exit={{ opacity: 0, scale: 0.95 }}
				transition={{ duration: 0.2, type: "spring", stiffness: 300, damping: 25 }}
			>
				{/* Header */}
				<header className="flex items-center justify-between px-6 py-4 bg-gradient-to-r from-mainColor to-mainColor/90 text-white rounded-t-xl">
					<div className="flex items-center gap-3">
						<FileText size={22} className="text-white" />
						<h2 className="font-semibold text-xl">Cupom Fiscal</h2>
					</div>

					<div className="flex items-center gap-2">
						<Button
							onClick={onClose}
							variant="ghost"
							size="icon"
							className="text-white hover:bg-white/20 rounded-full p-2"
							title="Fechar"
							aria-label="Fechar modal"
						>
							<X size={20} />
						</Button>
					</div>
				</header>

				{/* Content */}
				<main className="flex-1 bg-gray-50 overflow-auto p-4 relative">
					<AnimatePresence>
						{!pdfUrl ? (
							<motion.div
								className="flex items-center justify-center h-full"
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								exit={{ opacity: 0 }}
							>
								<div className="text-center">
									<motion.div
										className="w-14 h-14 border-4 border-mainColor border-t-transparent rounded-full mx-auto mb-4"
										animate={{ rotate: 360 }}
										transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
									></motion.div>
									<p className="text-gray-600 text-lg">Carregando PDF...</p>
								</div>
							</motion.div>
						) : isMobile ? (
							<motion.div
								className="flex flex-col items-center justify-center h-full space-y-6 p-4"
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								exit={{ opacity: 0 }}
							>
								<div className="text-center max-w-md">
									<div className="flex justify-center mb-6">
										<motion.div
											className="p-6 bg-mainColor/10 rounded-full"
											animate={{ scale: [1, 1.05, 1] }}
											transition={{ duration: 2, repeat: Infinity }}
										>
											<Eye size={48} className="text-mainColor" />
										</motion.div>
									</div>
									<h3 className="text-xl font-medium text-gray-800 mb-3">Dispositivo Móvel Detectado</h3>
									<p className="text-gray-600 mb-2 leading-relaxed">
										A visualização de PDF pode não funcionar corretamente em dispositivos móveis.
									</p>
									<p className="text-gray-600 mb-6 leading-relaxed">Utilize os botões abaixo para visualizar o documento.</p>
								</div>

								<div className="flex flex-col sm:flex-row gap-3 w-full max-w-md">
									<Button
										onClick={handleDownload}
										className="bg-mainColor hover:bg-mainColor/90 text-white px-6 py-5 rounded-lg font-medium flex-1 flex items-center justify-center gap-2 shadow-sm"
										aria-label="Baixar cupom fiscal"
									>
										<Download size={20} />
										<span>Baixar PDF</span>
									</Button>

									<Button
										onClick={handlePrint}
										className="bg-gray-700 hover:bg-gray-800 text-white px-6 py-5 rounded-lg font-medium flex-1 flex items-center justify-center gap-2 shadow-sm"
										aria-label="Imprimir cupom fiscal"
									>
										<Printer size={20} />
										<span>Imprimir</span>
									</Button>
								</div>
							</motion.div>
						) : (
							<motion.div className="w-full h-full" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
								<embed
									src={pdfUrl}
									type="application/pdf"
									width="100%"
									height="100%"
									className="w-full h-full rounded-lg shadow-lg border border-gray-200"
								/>
							</motion.div>
						)}
					</AnimatePresence>
				</main>
			</motion.div>
		</OverlayContainer>
	);
}
